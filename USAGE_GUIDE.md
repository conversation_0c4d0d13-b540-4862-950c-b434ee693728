# 禅道任务爬取插件使用指南

## 目标页面

本插件专门用于爬取禅道系统中的"我的工作-任务"页面：
- **URL**: `https://chandao.sw/pro/my-work-task.html`
- **页面内容**: 当前用户的任务列表

## 数据字段说明

根据您提供的页面截图，插件会抓取以下字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| ID | 任务唯一标识 | 124434 |
| P | 优先级 | 3 |
| 任务名称 | 具体任务描述 | 建筑104栋生主要项目 |
| 所属项目 | 任务归属项目 | 2024年集团平台日常维... |
| 所属版本 | 项目版本 | 版本 交付日-日常事务 |
| 创建者 | 任务创建人 | 于海涛 |
| 指派给 | 任务负责人 | - |
| 预计 | 预计工时 | 4h |
| 消耗 | 已用工时 | 0h |
| 剩余 | 剩余工时 | 4h |
| 进度 | 完成进度 | 0 |
| 认工日期 | 认工日期 | 2025-07-03 |
| 截止日期 | 任务截止时间 | 未开始 |
| 状态 | 当前状态 | - |

## 使用步骤

### 1. 启动插件
```bash
export JAVA_HOME=$(/usr/libexec/java_home -v17)
./gradlew runIde
```

### 2. 登录禅道
1. 在 IntelliJ IDEA 中点击 `Tools` → `打开禅道`
2. 选择 "在内嵌浏览器中登录"（推荐）
3. 在自动打开的浏览器中登录禅道系统
4. 确保能够访问 `https://chandao.sw/pro/my-work-task.html` 页面
5. 返回插件对话框，点击 "确认登录"

### 3. 查看数据
- 登录成功后，插件会自动爬取任务数据
- 数据显示在右侧的 "SW Cares" 工具窗口中
- 表格包含所有任务字段信息

### 4. 交互功能
- **双击任务行**: 在浏览器中打开该任务的详细页面
- **右键菜单**: 
  - "在浏览器中打开": 打开任务详情
  - "复制链接": 复制任务链接到剪贴板
- **刷新数据**: 点击工具栏的"刷新数据"按钮获取最新信息
- **打开原页面**: 点击"在浏览器中打开"直接访问任务列表页面

## 故障排查

### 1. 数据为空或不完整
**可能原因**:
- 页面结构发生变化
- 登录状态失效
- 网络连接问题

**解决方法**:
1. 检查是否能正常访问 `https://chandao.sw/pro/my-work-task.html`
2. 重新登录
3. 查看调试日志

### 2. 查看调试信息
插件会自动保存抓取到的HTML内容用于调试：
- **位置**: 系统临时目录下的 `chandao_debug.html` 文件
- **macOS**: `/tmp/chandao_debug.html`
- **Windows**: `%TEMP%\chandao_debug.html`
- **Linux**: `/tmp/chandao_debug.html`

您可以打开这个文件查看实际抓取到的页面内容，确认：
- 是否成功获取到任务列表页面
- 页面结构是否与预期一致
- 是否包含所需的数据

### 3. 页面结构变化
如果禅道系统更新导致页面结构变化，您可以：
1. 查看 `chandao_debug.html` 文件
2. 找到新的表格结构
3. 联系开发者更新解析逻辑

### 4. 网络问题
- 确保能够访问 `https://chandao.sw/pro`
- 检查防火墙和代理设置
- 插件已配置忽略SSL证书验证，适用于内网环境

## 高级功能

### 1. 手动设置会话信息
如果您知道具体的会话信息，可以在登录对话框中手动输入：
- **Session ID**: PHP会话ID
- **用户令牌**: 禅道用户令牌

### 2. 自定义URL
如果您的禅道系统URL不同，可以修改 `CrawlerConfig` 中的配置：
```kotlin
data class CrawlerConfig(
    val baseUrl: String = "https://your-chandao-url",
    val myWorkTaskPath: String = "/my-work-task.html",
    // ...
)
```

## 数据导出

目前插件支持：
- 在表格中查看所有数据
- 复制单个任务链接
- 在浏览器中打开任务详情

未来可能添加的功能：
- 导出为CSV文件
- 导出为Excel文件
- 数据过滤和搜索

## 性能说明

- **数据刷新**: 每次刷新会重新请求服务器
- **缓存**: 数据在会话期间保持，重启IDE后需要重新登录
- **网络超时**: 默认30秒，可在配置中调整

## 安全说明

- 插件不会保存您的用户名和密码
- 会话信息仅在内存中保持
- 所有网络请求都通过HTTPS加密
- 支持内网自签名证书
