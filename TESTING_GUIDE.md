# 禅道插件测试指南

## 测试环境准备

1. **启动插件开发环境**
   ```bash
   export JAVA_HOME=$(/usr/libexec/java_home -v17)
   ./gradlew runIde
   ```

2. **等待 IntelliJ IDEA 启动**
   - 启动过程可能需要 1-2 分钟
   - 看到 IDE 界面后即可开始测试

## 功能测试步骤

### 1. 基本功能测试

1. **找到插件菜单**
   - 在 IntelliJ IDEA 中，点击顶部菜单栏的 `Tools`
   - 查找 `打开禅道` 选项

2. **测试工具窗口**
   - 查看右侧是否有 `SW Cares` 工具窗口
   - 如果没有显示，可以通过 `View` → `Tool Windows` → `SW Cares` 打开

### 2. 登录功能测试

1. **触发登录流程**
   - 点击 `Tools` → `打开禅道`
   - 应该会弹出登录选择对话框，现在有4个选项

2. **测试嵌入式浏览器登录**（推荐方式）
   - 选择 "在内嵌浏览器中登录"
   - 应该会弹出一个对话框，同时自动在外部浏览器中打开禅道登录页面
   - 在浏览器中完成登录
   - 返回对话框，点击 "确认登录" 按钮
   - **高级选项测试**：
     - 如果知道会话信息，可以在 "Session ID" 和 "用户令牌" 字段中输入
     - 点击 "确认登录"

3. **测试外部浏览器登录**
   - 选择 "在外部浏览器中登录"
   - 检查是否正确打开了禅道登录页面：`https://chandao.sw/pro/user-login.html`
   - 在浏览器中完成登录
   - 返回 IDE，点击 "开始爬取"

4. **测试对话框登录**
   - 选择 "在对话框中登录"
   - 输入正确的用户名和密码
   - 点击 "登录" 按钮

### 3. 数据爬取测试

1. **检查数据显示**
   - 登录成功后，查看 `SW Cares` 工具窗口
   - 应该显示一个表格，包含以下列：
     - ID
     - 标题
     - 类型
     - 状态
     - 优先级
     - 指派给

2. **测试表格功能**
   - **双击记录**：应该在浏览器中打开对应的禅道页面
   - **右键菜单**：
     - "在浏览器中打开"：打开对应页面
     - "复制链接"：复制链接到剪贴板

### 4. 工具窗口功能测试

1. **刷新数据**
   - 点击工具窗口中的 "刷新数据" 按钮
   - 检查数据是否更新

2. **在浏览器中打开**
   - 点击 "在浏览器中打开" 按钮
   - 应该打开禅道的我的页面：`https://chandao.sw/pro/my/`

3. **登出功能**
   - 点击 "登出" 按钮
   - 确认登出对话框
   - 检查表格数据是否清空

## 预期行为

### 成功场景
- ✅ 插件菜单项正确显示
- ✅ 工具窗口正确创建和显示
- ✅ 登录流程顺利完成
- ✅ 数据正确爬取和显示
- ✅ 表格交互功能正常
- ✅ 浏览器链接正确打开

### 错误处理
- ❌ **网络错误**：显示友好的错误消息
- ❌ **登录失败**：显示登录失败提示
- ❌ **SSL证书问题**：已配置忽略内网证书验证
- ❌ **数据解析失败**：显示解析错误信息

## 常见问题排查

### 1. 插件未加载
- 检查构建是否成功
- 查看 IDE 日志中是否有插件加载错误

### 2. 网络连接问题
- 确保可以访问 `https://chandao.sw/pro`
- 检查防火墙和代理设置

### 3. SSL 证书错误
- 插件已配置忽略证书验证
- 如果仍有问题，检查网络配置

### 4. 数据显示为空
- 检查登录状态
- 确认禅道页面结构是否发生变化
- 查看日志中的解析错误信息

### 5. 工具窗口不显示
- 手动打开：`View` → `Tool Windows` → `SW Cares`
- 重启 IDE 后重试

## 日志查看

如果遇到问题，可以查看详细日志：

1. **IDE 控制台**
   - 在开发环境中，日志会直接显示在启动 `runIde` 的终端中

2. **IDE 日志文件**
   - macOS: `~/Library/Logs/JetBrains/IntelliJIdea{version}/idea.log`
   - 搜索 "qhyu.asia.swcares" 相关的日志条目

## 测试数据

为了更好地测试，建议在禅道系统中准备一些测试数据：
- 创建几个不同状态的任务/Bug
- 分配给不同的用户
- 设置不同的优先级

## 性能测试

- 测试大量数据的加载性能
- 检查内存使用情况
- 验证长时间运行的稳定性

## 安全测试

- 确认密码不会被保存或记录
- 验证会话管理的安全性
- 检查敏感信息的处理
