<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>qhyu.asia.swcares</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>Swcares</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor url="https://www.yourcompany.com">YourCompany</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
    SW Cares Plugin - 禅道数据爬取工具<br>
    <em>用于在IDEA中打开禅道网站并爬取相关数据</em>
  ]]></description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- Tool Window -->
        <toolWindow id="SW Cares" secondary="true" anchor="right"
                    factoryClass="qhyu.asia.swcares.ui.ChandaoToolWindowFactory"/>
    </extensions>

    <!-- Actions -->
    <actions>
        <action id="qhyu.asia.swcares.OpenChandaoAction"
                class="qhyu.asia.swcares.actions.OpenChandaoAction"
                text="打开禅道"
                description="打开禅道网站并爬取数据">
            <add-to-group group-id="ToolsMenu" anchor="first"/>
        </action>
    </actions>
</idea-plugin>