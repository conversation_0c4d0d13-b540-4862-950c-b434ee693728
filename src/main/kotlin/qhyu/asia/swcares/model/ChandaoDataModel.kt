package qhyu.asia.swcares.model

import com.google.gson.annotations.SerializedName
import java.time.LocalDateTime

/**
 * 禅道任务数据模型
 */
data class ChandaoRecord(
    val id: String,                    // ID
    val priority: String,              // P (优先级)
    val taskName: String,              // 任务名称
    val projectName: String,           // 所属项目
    val projectVersion: String,        // 所属版本
    val createdBy: String,             // 创建者
    val assignedTo: String,            // 指派给
    val estimatedHours: String,        // 预计
    val consumedHours: String,         // 消耗
    val leftHours: String,             // 剩余
    val progress: String,              // 进度
    val recognitionDate: String,       // 认工日期
    val deadline: String,              // 截止日期
    val status: String,                // 状态
    val url: String                    // 链接
)

/**
 * 禅道项目信息
 */
data class ChandaoProject(
    val id: String,
    val name: String,
    val code: String,
    val status: String,
    val description: String
)

/**
 * 禅道用户信息
 */
data class ChandaoUser(
    val account: String,
    val realname: String,
    val role: String,
    val dept: String,
    val email: String
)

/**
 * 登录响应
 */
data class LoginResponse(
    val success: <PERSON>ole<PERSON>,
    val message: String,
    val sessionId: String? = null,
    val user: ChandaoUser? = null
)

/**
 * API响应包装类
 */
data class ApiResponse<T>(
    val status: String,
    val data: T? = null,
    val message: String? = null
)

/**
 * 爬取配置
 */
data class CrawlerConfig(
    val baseUrl: String = "https://chandao.sw/pro",
    val loginPath: String = "/user-login.html",
    val myPath: String = "/my/",
    val myWorkTaskPath: String = "/my-work-task.html",
    val timeout: Long = 30000,
    val retryCount: Int = 3
)

/**
 * 爬取结果
 */
data class CrawlerResult(
    val success: Boolean,
    val message: String,
    val records: List<ChandaoRecord> = emptyList(),
    val timestamp: LocalDateTime = LocalDateTime.now()
)
