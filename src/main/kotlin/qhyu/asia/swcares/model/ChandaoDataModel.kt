package qhyu.asia.swcares.model

import com.google.gson.annotations.SerializedName
import java.time.LocalDateTime

/**
 * 禅道数据模型
 */
data class ChandaoRecord(
    val id: String,
    val title: String,
    val type: String,
    val status: String,
    val priority: String,
    val assignedTo: String,
    val createdBy: String,
    val createdDate: String,
    val lastEditedDate: String,
    val description: String,
    val url: String
)

/**
 * 禅道项目信息
 */
data class ChandaoProject(
    val id: String,
    val name: String,
    val code: String,
    val status: String,
    val description: String
)

/**
 * 禅道用户信息
 */
data class ChandaoUser(
    val account: String,
    val realname: String,
    val role: String,
    val dept: String,
    val email: String
)

/**
 * 登录响应
 */
data class LoginResponse(
    val success: Boolean,
    val message: String,
    val sessionId: String? = null,
    val user: ChandaoUser? = null
)

/**
 * API响应包装类
 */
data class ApiResponse<T>(
    val status: String,
    val data: T? = null,
    val message: String? = null
)

/**
 * 爬取配置
 */
data class CrawlerConfig(
    val baseUrl: String = "https://chandao.sw/pro",
    val loginPath: String = "/user-login.html",
    val myPath: String = "/my/",
    val timeout: Long = 30000,
    val retryCount: Int = 3
)

/**
 * 爬取结果
 */
data class CrawlerResult(
    val success: Boolean,
    val message: String,
    val records: List<ChandaoRecord> = emptyList(),
    val timestamp: LocalDateTime = LocalDateTime.now()
)
