package qhyu.asia.swcares.actions

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.wm.ToolWindowManager
import kotlinx.coroutines.*
import qhyu.asia.swcares.service.ChandaoWebService
import qhyu.asia.swcares.ui.LoginDialog
import qhyu.asia.swcares.ui.WebBrowserDialog
import javax.swing.SwingUtilities

class OpenChandaoAction : AnAction() {
    
    private val logger = thisLogger()
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val webService = ApplicationManager.getApplication().service<ChandaoWebService>()
        
        logger.info("OpenChandaoAction triggered")
        
        // 在后台任务中执行
        ProgressManager.getInstance().run(object : Task.Backgroundable(project, "检查禅道登录状态", true) {
            override fun run(indicator: ProgressIndicator) {
                indicator.text = "正在检查登录状态..."
                
                runBlocking {
                    try {
                        val isLoggedIn = webService.checkLoginStatus()
                        
                        SwingUtilities.invokeLater {
                            if (isLoggedIn) {
                                // 已登录，直接爬取数据
                                crawlDataAndShowResults(project, webService)
                            } else {
                                // 未登录，显示选择对话框
                                showLoginOptions(project, webService)
                            }
                        }
                    } catch (e: Exception) {
                        logger.error("Error checking login status", e)
                        SwingUtilities.invokeLater {
                            Messages.showErrorDialog(
                                project,
                                "检查登录状态时发生错误: ${e.message}",
                                "错误"
                            )
                        }
                    }
                }
            }
        })
    }
    
    private fun showLoginOptions(project: Project, webService: ChandaoWebService) {
        val options = arrayOf("在内嵌浏览器中登录", "在外部浏览器中登录", "在对话框中登录", "取消")
        val choice = Messages.showDialog(
            project,
            "检测到您尚未登录禅道系统，请选择登录方式：",
            "禅道登录",
            options,
            0,
            Messages.getQuestionIcon()
        )

        when (choice) {
            0 -> {
                // 在内嵌浏览器中登录
                showWebBrowserDialog(project, webService)
            }
            1 -> {
                // 在外部浏览器中登录
                openInBrowser(project, webService)
            }
            2 -> {
                // 在对话框中登录
                showLoginDialog(project, webService)
            }
            else -> {
                // 取消
                logger.info("User cancelled login")
            }
        }
    }
    
    private fun showWebBrowserDialog(project: Project, webService: ChandaoWebService) {
        val config = webService.getConfig()
        val loginUrl = "${config.baseUrl}${config.loginPath}"

        logger.info("Opening web browser dialog with URL: $loginUrl")

        val dialog = WebBrowserDialog(project, loginUrl) { cookies ->
            // 登录成功回调
            logger.info("Login successful via web browser, received ${cookies.size} cookies")

            // 设置登录 Cookie
            webService.setLoginCookies(cookies)

            // 显示成功消息并开始爬取数据
            SwingUtilities.invokeLater {
                Messages.showInfoMessage(project, "登录成功！正在爬取数据...", "成功")
                crawlDataAndShowResults(project, webService)
            }
        }

        dialog.show()
    }

    private fun openInBrowser(project: Project, webService: ChandaoWebService) {
        val config = webService.getConfig()
        val loginUrl = "${config.baseUrl}${config.loginPath}"
        
        logger.info("Opening browser with URL: $loginUrl")
        BrowserUtil.browse(loginUrl)
        
        // 显示提示信息
        val result = Messages.showYesNoDialog(
            project,
            "已在浏览器中打开禅道登录页面。\n请在浏览器中完成登录，然后点击是开始爬取数据。",
            "浏览器登录",
            "开始爬取",
            "取消",
            Messages.getInformationIcon()
        )
        
        if (result == Messages.YES) {
            crawlDataAndShowResults(project, webService)
        }
    }
    
    private fun showLoginDialog(project: Project, webService: ChandaoWebService) {
        val dialog = LoginDialog(project)
        
        if (dialog.showAndGet()) {
            val username = dialog.getUsername()
            val password = dialog.getPassword()
            
            if (username.isNotEmpty() && password.isNotEmpty()) {
                performLogin(project, webService, username, password)
            } else {
                Messages.showWarningDialog(project, "请输入用户名和密码", "警告")
            }
        }
    }
    
    private fun performLogin(project: Project, webService: ChandaoWebService, username: String, password: String) {
        ProgressManager.getInstance().run(object : Task.Backgroundable(project, "正在登录禅道", true) {
            override fun run(indicator: ProgressIndicator) {
                indicator.text = "正在验证用户凭据..."
                
                runBlocking {
                    try {
                        val loginResult = webService.login(username, password)
                        
                        SwingUtilities.invokeLater {
                            if (loginResult.success) {
                                Messages.showInfoMessage(project, "登录成功！", "成功")
                                crawlDataAndShowResults(project, webService)
                            } else {
                                Messages.showErrorDialog(project, loginResult.message, "登录失败")
                            }
                        }
                    } catch (e: Exception) {
                        logger.error("Error during login", e)
                        SwingUtilities.invokeLater {
                            Messages.showErrorDialog(
                                project,
                                "登录过程中发生错误: ${e.message}",
                                "错误"
                            )
                        }
                    }
                }
            }
        })
    }
    
    private fun crawlDataAndShowResults(project: Project, webService: ChandaoWebService) {
        ProgressManager.getInstance().run(object : Task.Backgroundable(project, "正在爬取禅道数据", true) {
            override fun run(indicator: ProgressIndicator) {
                indicator.text = "正在获取数据..."
                
                runBlocking {
                    try {
                        val crawlResult = webService.crawlMyPageData()
                        
                        SwingUtilities.invokeLater {
                            if (crawlResult.success) {
                                // 显示工具窗口
                                showToolWindow(project, crawlResult)
                                
                                Messages.showInfoMessage(
                                    project,
                                    "成功爬取 ${crawlResult.records.size} 条记录",
                                    "爬取完成"
                                )
                            } else {
                                Messages.showErrorDialog(project, crawlResult.message, "爬取失败")
                            }
                        }
                    } catch (e: Exception) {
                        logger.error("Error during data crawling", e)
                        SwingUtilities.invokeLater {
                            Messages.showErrorDialog(
                                project,
                                "爬取数据时发生错误: ${e.message}",
                                "错误"
                            )
                        }
                    }
                }
            }
        })
    }
    
    private fun showToolWindow(project: Project, crawlResult: qhyu.asia.swcares.model.CrawlerResult) {
        val toolWindowManager = ToolWindowManager.getInstance(project)
        val toolWindow = toolWindowManager.getToolWindow("SW Cares")
        
        if (toolWindow != null) {
            // 更新工具窗口内容
            val contentManager = toolWindow.contentManager
            if (contentManager.contentCount > 0) {
                val content = contentManager.getContent(0)
                val component = content?.component
                if (component is qhyu.asia.swcares.ui.ChandaoToolWindowPanel) {
                    component.updateData(crawlResult.records)
                }
            }
            
            // 显示工具窗口
            toolWindow.show()
        } else {
            logger.warn("Tool window 'SW Cares' not found")
        }
    }
}
