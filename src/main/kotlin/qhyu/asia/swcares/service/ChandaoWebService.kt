package qhyu.asia.swcares.service

import com.google.gson.Gson
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import qhyu.asia.swcares.model.*
import java.io.IOException
import java.net.CookieManager
import java.net.CookiePolicy
import java.util.concurrent.TimeUnit

@Service
class ChandaoWebService {
    
    private val logger = thisLogger()
    private val gson = Gson()
    private val config = CrawlerConfig()
    
    private val cookieManager = CookieManager().apply {
        setCookiePolicy(CookiePolicy.ACCEPT_ALL)
    }
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(config.timeout, TimeUnit.MILLISECONDS)
        .readTimeout(config.timeout, TimeUnit.MILLISECONDS)
        .writeTimeout(config.timeout, TimeUnit.MILLISECONDS)
        .cookieJar(JavaNetCookieJar(cookieManager))
        .build()
    
    private var isLoggedIn = false
    private var currentUser: ChandaoUser? = null
    
    /**
     * 检查登录状态
     */
    suspend fun checkLoginStatus(): Boolean = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url("${config.baseUrl}${config.myPath}")
                .build()
            
            val response = client.newCall(request).execute()
            val body = response.body?.string() ?: ""
            
            // 检查是否被重定向到登录页面
            isLoggedIn = !body.contains("user-login") && response.isSuccessful
            logger.info("Login status checked: $isLoggedIn")
            
            isLoggedIn
        } catch (e: Exception) {
            logger.error("Error checking login status", e)
            false
        }
    }
    
    /**
     * 获取登录页面的必要信息（如CSRF token等）
     */
    suspend fun getLoginPageInfo(): Map<String, String> = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url("${config.baseUrl}${config.loginPath}")
                .build()
            
            val response = client.newCall(request).execute()
            val html = response.body?.string() ?: ""
            val doc = Jsoup.parse(html)
            
            val result = mutableMapOf<String, String>()
            
            // 提取CSRF token或其他必要的隐藏字段
            doc.select("input[type=hidden]").forEach { input ->
                val name = input.attr("name")
                val value = input.attr("value")
                if (name.isNotEmpty()) {
                    result[name] = value
                }
            }
            
            logger.info("Login page info extracted: ${result.keys}")
            result
        } catch (e: Exception) {
            logger.error("Error getting login page info", e)
            emptyMap()
        }
    }
    
    /**
     * 执行登录
     */
    suspend fun login(username: String, password: String): LoginResponse = withContext(Dispatchers.IO) {
        try {
            // 首先获取登录页面信息
            val loginPageInfo = getLoginPageInfo()
            
            // 构建登录请求
            val formBody = FormBody.Builder()
                .add("account", username)
                .add("password", password)
            
            // 添加从登录页面获取的隐藏字段
            loginPageInfo.forEach { (key, value) ->
                formBody.add(key, value)
            }
            
            val request = Request.Builder()
                .url("${config.baseUrl}/user-login.html")
                .post(formBody.build())
                .build()
            
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            if (response.isSuccessful) {
                // 检查登录是否成功（通常通过检查响应内容或重定向）
                if (!responseBody.contains("登录失败") && !responseBody.contains("用户名或密码错误")) {
                    isLoggedIn = true
                    // 这里可以解析用户信息
                    currentUser = ChandaoUser(
                        account = username,
                        realname = username, // 实际应该从响应中解析
                        role = "",
                        dept = "",
                        email = ""
                    )
                    
                    logger.info("Login successful for user: $username")
                    LoginResponse(true, "登录成功", null, currentUser)
                } else {
                    logger.warn("Login failed for user: $username")
                    LoginResponse(false, "用户名或密码错误")
                }
            } else {
                logger.error("Login request failed with code: ${response.code}")
                LoginResponse(false, "登录请求失败")
            }
        } catch (e: Exception) {
            logger.error("Error during login", e)
            LoginResponse(false, "登录过程中发生错误: ${e.message}")
        }
    }
    
    /**
     * 爬取我的页面数据
     */
    suspend fun crawlMyPageData(): CrawlerResult = withContext(Dispatchers.IO) {
        try {
            if (!isLoggedIn) {
                return@withContext CrawlerResult(false, "用户未登录")
            }
            
            val request = Request.Builder()
                .url("${config.baseUrl}${config.myPath}")
                .build()
            
            val response = client.newCall(request).execute()
            val html = response.body?.string() ?: ""
            
            if (!response.isSuccessful) {
                return@withContext CrawlerResult(false, "请求失败: ${response.code}")
            }
            
            val records = parseMyPageData(html)
            logger.info("Crawled ${records.size} records from my page")
            
            CrawlerResult(true, "数据爬取成功", records)
        } catch (e: Exception) {
            logger.error("Error crawling my page data", e)
            CrawlerResult(false, "爬取数据时发生错误: ${e.message}")
        }
    }
    
    /**
     * 解析我的页面数据
     */
    private fun parseMyPageData(html: String): List<ChandaoRecord> {
        val records = mutableListOf<ChandaoRecord>()
        
        try {
            val doc = Jsoup.parse(html)
            
            // 这里需要根据实际的禅道页面结构来解析
            // 以下是示例解析逻辑，需要根据实际页面调整
            doc.select("table tbody tr").forEach { row ->
                val cells = row.select("td")
                if (cells.size >= 6) {
                    val record = ChandaoRecord(
                        id = cells[0].text().trim(),
                        title = cells[1].text().trim(),
                        type = cells[2].text().trim(),
                        status = cells[3].text().trim(),
                        priority = cells[4].text().trim(),
                        assignedTo = cells[5].text().trim(),
                        createdBy = "",
                        createdDate = "",
                        lastEditedDate = "",
                        description = "",
                        url = "${config.baseUrl}${cells[1].select("a").attr("href")}"
                    )
                    records.add(record)
                }
            }
        } catch (e: Exception) {
            logger.error("Error parsing my page data", e)
        }
        
        return records
    }
    
    /**
     * 获取当前用户信息
     */
    fun getCurrentUser(): ChandaoUser? = currentUser
    
    /**
     * 登出
     */
    suspend fun logout() = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url("${config.baseUrl}/user-logout.html")
                .build()
            
            client.newCall(request).execute()
            isLoggedIn = false
            currentUser = null
            cookieManager.cookieStore.removeAll()
            
            logger.info("User logged out successfully")
        } catch (e: Exception) {
            logger.error("Error during logout", e)
        }
    }
    
    /**
     * 获取配置
     */
    fun getConfig(): CrawlerConfig = config
}
