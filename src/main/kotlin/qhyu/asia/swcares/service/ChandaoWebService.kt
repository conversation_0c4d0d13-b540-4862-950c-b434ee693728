package qhyu.asia.swcares.service

import com.google.gson.Gson
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import qhyu.asia.swcares.model.*
import java.io.IOException
import java.net.HttpCookie
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.*

@Service
class ChandaoWebService {
    
    private val logger = thisLogger()
    private val gson = Gson()
    private val config = CrawlerConfig()
    
    private val cookieJar = SimpleCookieJar()

    class SimpleCookieJar : CookieJar {
        private val cookieStore = mutableMapOf<String, MutableList<Cookie>>()

        override fun saveFromResponse(url: HttpUrl, cookies: List<Cookie>) {
            val host = url.host
            cookieStore[host] = cookies.toMutableList()
        }

        override fun loadForRequest(url: HttpUrl): List<Cookie> {
            return cookieStore[url.host] ?: emptyList()
        }

        fun addCookie(url: String, cookie: Cookie) {
            try {
                val httpUrl = HttpUrl.Builder()
                    .scheme("https")
                    .host("chandao.sw")
                    .build()
                val host = httpUrl.host
                cookieStore.getOrPut(host) { mutableListOf() }.add(cookie)
            } catch (e: Exception) {
                // 如果解析失败，直接使用域名
                cookieStore.getOrPut("chandao.sw") { mutableListOf() }.add(cookie)
            }
        }

        fun clear() {
            cookieStore.clear()
        }
    }

    private val client = OkHttpClient.Builder()
        .connectTimeout(config.timeout, TimeUnit.MILLISECONDS)
        .readTimeout(config.timeout, TimeUnit.MILLISECONDS)
        .writeTimeout(config.timeout, TimeUnit.MILLISECONDS)
        .cookieJar(cookieJar)
        .apply {
            // 为内网环境配置SSL，忽略证书验证
            try {
                val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
                    override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
                    override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {}
                    override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
                })

                val sslContext = SSLContext.getInstance("SSL")
                sslContext.init(null, trustAllCerts, java.security.SecureRandom())

                sslSocketFactory(sslContext.socketFactory, trustAllCerts[0] as X509TrustManager)
                hostnameVerifier { _, _ -> true }
            } catch (e: Exception) {
                logger.warn("Failed to configure SSL for internal network", e)
            }
        }
        .build()
    
    private var isLoggedIn = false
    private var currentUser: ChandaoUser? = null
    
    /**
     * 检查登录状态
     */
    suspend fun checkLoginStatus(): Boolean = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url("${config.baseUrl}${config.myPath}")
                .build()
            
            val response = client.newCall(request).execute()
            val body = response.body?.string() ?: ""
            
            // 检查是否被重定向到登录页面
            isLoggedIn = !body.contains("user-login") && response.isSuccessful
            logger.info("Login status checked: $isLoggedIn")
            
            isLoggedIn
        } catch (e: Exception) {
            logger.error("Error checking login status", e)
            false
        }
    }
    
    /**
     * 获取登录页面的必要信息（如CSRF token等）
     */
    suspend fun getLoginPageInfo(): Map<String, String> = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url("${config.baseUrl}${config.loginPath}")
                .build()
            
            val response = client.newCall(request).execute()
            val html = response.body?.string() ?: ""
            val doc = Jsoup.parse(html)
            
            val result = mutableMapOf<String, String>()
            
            // 提取CSRF token或其他必要的隐藏字段
            doc.select("input[type=hidden]").forEach { input ->
                val name = input.attr("name")
                val value = input.attr("value")
                if (name.isNotEmpty()) {
                    result[name] = value
                }
            }
            
            logger.info("Login page info extracted: ${result.keys}")
            result
        } catch (e: Exception) {
            logger.error("Error getting login page info", e)
            emptyMap()
        }
    }
    
    /**
     * 使用从浏览器获取的 Cookie 设置登录状态
     */
    fun setLoginCookies(cookies: Map<String, String>) {
        try {
            cookies.forEach { (name, value) ->
                val cookie = Cookie.Builder()
                    .domain("chandao.sw")
                    .name(name)
                    .value(value)
                    .build()

                cookieJar.addCookie("https://chandao.sw", cookie)
            }

            // 如果用户确认了登录，即使没有具体的 Cookie 也标记为已登录
            if (cookies.containsKey("user_confirmed_login") || cookies.isNotEmpty()) {
                isLoggedIn = true
                logger.info("Login status set successfully, ${cookies.size} cookies added")
            }
        } catch (e: Exception) {
            logger.error("Error setting login cookies", e)
        }
    }

    /**
     * 执行登录
     */
    suspend fun login(username: String, password: String): LoginResponse = withContext(Dispatchers.IO) {
        try {
            // 首先获取登录页面信息
            val loginPageInfo = getLoginPageInfo()
            
            // 构建登录请求
            val formBody = FormBody.Builder()
                .add("account", username)
                .add("password", password)
            
            // 添加从登录页面获取的隐藏字段
            loginPageInfo.forEach { (key, value) ->
                formBody.add(key, value)
            }
            
            val request = Request.Builder()
                .url("${config.baseUrl}/user-login.html")
                .post(formBody.build())
                .build()
            
            val response = client.newCall(request).execute()
            val responseBody = response.body?.string() ?: ""
            
            if (response.isSuccessful) {
                // 检查登录是否成功（通常通过检查响应内容或重定向）
                if (!responseBody.contains("登录失败") && !responseBody.contains("用户名或密码错误")) {
                    isLoggedIn = true
                    // 这里可以解析用户信息
                    currentUser = ChandaoUser(
                        account = username,
                        realname = username, // 实际应该从响应中解析
                        role = "",
                        dept = "",
                        email = ""
                    )
                    
                    logger.info("Login successful for user: $username")
                    LoginResponse(true, "登录成功", null, currentUser)
                } else {
                    logger.warn("Login failed for user: $username")
                    LoginResponse(false, "用户名或密码错误")
                }
            } else {
                logger.error("Login request failed with code: ${response.code}")
                LoginResponse(false, "登录请求失败")
            }
        } catch (e: Exception) {
            logger.error("Error during login", e)
            LoginResponse(false, "登录过程中发生错误: ${e.message}")
        }
    }
    
    /**
     * 爬取我的工作任务页面数据
     */
    suspend fun crawlMyPageData(): CrawlerResult = withContext(Dispatchers.IO) {
        try {
            if (!isLoggedIn) {
                return@withContext CrawlerResult(false, "用户未登录")
            }

            val request = Request.Builder()
                .url("${config.baseUrl}${config.myWorkTaskPath}")
                .build()

            val response = client.newCall(request).execute()
            val html = response.body?.string() ?: ""

            if (!response.isSuccessful) {
                return@withContext CrawlerResult(false, "请求失败: ${response.code}")
            }

            // 保存HTML内容用于调试（可选）
            saveDebugHtml(html)

            val records = parseMyWorkTaskData(html)
            logger.info("Crawled ${records.size} task records from my work page")

            CrawlerResult(true, "任务数据爬取成功", records)
        } catch (e: Exception) {
            logger.error("Error crawling my work task data", e)
            CrawlerResult(false, "爬取任务数据时发生错误: ${e.message}")
        }
    }
    
    /**
     * 解析我的工作任务页面数据
     */
    private fun parseMyWorkTaskData(html: String): List<ChandaoRecord> {
        val records = mutableListOf<ChandaoRecord>()

        try {
            val doc = Jsoup.parse(html)

            // 调试：记录页面基本信息
            logger.info("Parsing HTML page, title: ${doc.title()}")
            logger.info("Found ${doc.select("table").size} tables in the page")

            // 查找任务表格，通常在 table 标签中
            val taskTable = doc.select("table").firstOrNull { table ->
                // 查找包含任务数据的表格，通过表头来识别
                val headers = table.select("thead tr th, tr th")
                val headerTexts = headers.map { it.text() }
                logger.info("Table headers: $headerTexts")
                headers.any { it.text().contains("ID") || it.text().contains("任务名称") || it.text().contains("P") }
            }

            taskTable?.select("tbody tr")?.forEach { row ->
                val cells = row.select("td")

                // 根据您提供的表格结构解析数据
                // ID | P | 任务名称 | 所属项目 | 所属版本 | 创建者 | 指派给 | 预计 | 消耗 | 剩余 | 进度 | 认工日期 | 截止日期 | 状态
                if (cells.size >= 14) {
                    try {
                        val taskLink = cells[2].select("a").first()
                        val taskUrl = if (taskLink != null) {
                            val href = taskLink.attr("href")
                            if (href.startsWith("http")) href else "${config.baseUrl}$href"
                        } else {
                            "${config.baseUrl}${config.myWorkTaskPath}"
                        }

                        val record = ChandaoRecord(
                            id = cells[0].text().trim(),                    // ID
                            priority = cells[1].text().trim(),             // P (优先级)
                            taskName = cells[2].text().trim(),             // 任务名称
                            projectName = cells[3].text().trim(),          // 所属项目
                            projectVersion = cells[4].text().trim(),       // 所属版本
                            createdBy = cells[5].text().trim(),            // 创建者
                            assignedTo = cells[6].text().trim(),           // 指派给
                            estimatedHours = cells[7].text().trim(),       // 预计
                            consumedHours = cells[8].text().trim(),        // 消耗
                            leftHours = cells[9].text().trim(),            // 剩余
                            progress = cells[10].text().trim(),            // 进度
                            recognitionDate = cells[11].text().trim(),     // 认工日期
                            deadline = cells[12].text().trim(),            // 截止日期
                            status = cells[13].text().trim(),              // 状态
                            url = taskUrl
                        )
                        records.add(record)
                    } catch (e: Exception) {
                        logger.warn("Error parsing task row: ${e.message}")
                        // 继续处理下一行
                    }
                }
            }

            // 如果没有找到标准表格，尝试其他可能的结构
            if (records.isEmpty()) {
                // 尝试查找其他可能的表格结构
                doc.select("tr").forEach { row ->
                    val cells = row.select("td")
                    if (cells.size >= 5 && cells[0].text().matches(Regex("\\d+"))) {
                        // 简化版本的解析
                        val record = ChandaoRecord(
                            id = cells[0].text().trim(),
                            priority = if (cells.size > 1) cells[1].text().trim() else "",
                            taskName = if (cells.size > 2) cells[2].text().trim() else "",
                            projectName = if (cells.size > 3) cells[3].text().trim() else "",
                            projectVersion = if (cells.size > 4) cells[4].text().trim() else "",
                            createdBy = if (cells.size > 5) cells[5].text().trim() else "",
                            assignedTo = if (cells.size > 6) cells[6].text().trim() else "",
                            estimatedHours = if (cells.size > 7) cells[7].text().trim() else "",
                            consumedHours = if (cells.size > 8) cells[8].text().trim() else "",
                            leftHours = if (cells.size > 9) cells[9].text().trim() else "",
                            progress = if (cells.size > 10) cells[10].text().trim() else "",
                            recognitionDate = if (cells.size > 11) cells[11].text().trim() else "",
                            deadline = if (cells.size > 12) cells[12].text().trim() else "",
                            status = if (cells.size > 13) cells[13].text().trim() else "",
                            url = "${config.baseUrl}${config.myWorkTaskPath}"
                        )
                        records.add(record)
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("Error parsing my work task data", e)
        }

        return records
    }
    
    /**
     * 获取当前用户信息
     */
    fun getCurrentUser(): ChandaoUser? = currentUser
    
    /**
     * 登出
     */
    suspend fun logout() = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url("${config.baseUrl}/user-logout.html")
                .build()
            
            client.newCall(request).execute()
            isLoggedIn = false
            currentUser = null
            // Clear cookies
            cookieJar.clear()
            
            logger.info("User logged out successfully")
        } catch (e: Exception) {
            logger.error("Error during logout", e)
        }
    }
    
    /**
     * 保存调试HTML内容
     */
    private fun saveDebugHtml(html: String) {
        try {
            val debugFile = java.io.File(System.getProperty("java.io.tmpdir"), "chandao_debug.html")
            debugFile.writeText(html, Charsets.UTF_8)
            logger.info("Debug HTML saved to: ${debugFile.absolutePath}")
        } catch (e: Exception) {
            logger.warn("Failed to save debug HTML: ${e.message}")
        }
    }

    /**
     * 获取配置
     */
    fun getConfig(): CrawlerConfig = config
}
