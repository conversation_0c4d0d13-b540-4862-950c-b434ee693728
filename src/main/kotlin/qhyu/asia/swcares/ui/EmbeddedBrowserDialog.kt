package qhyu.asia.swcares.ui

import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.jcef.JBCefBrowser
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.*

class EmbeddedBrowserDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private var browser: JBCefBrowser? = null
    private var isLoginDetected = false
    private val extractedCookies = mutableMapOf<String, String>()

    init {
        title = "禅道登录 - 内嵌浏览器"
        init()
        setSize(1200, 800)
        setResizable(true)
    }
    
    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        try {
            // 检查JCEF是否可用
            if (!com.intellij.ui.jcef.JBCefApp.isSupported()) {
                throw Exception("JCEF not supported in this IDE version")
            }

            // 创建内嵌浏览器
            browser = JBCefBrowser.createBuilder()
                .setUrl(initialUrl)
                .setOffScreenRendering(false)
                .build()

            // 添加加载状态监听
            val loadingLabel = JBLabel("正在加载页面...")
            loadingLabel.horizontalAlignment = SwingConstants.CENTER
            loadingLabel.border = JBUI.Borders.empty(10)

            val browserPanel = JPanel(BorderLayout())
            browserPanel.add(loadingLabel, BorderLayout.NORTH)

            // 延迟添加浏览器组件，确保初始化完成
            SwingUtilities.invokeLater {
                try {
                    val browserComponent = browser?.component
                    if (browserComponent != null) {
                        browserPanel.remove(loadingLabel)
                        browserPanel.add(browserComponent, BorderLayout.CENTER)
                        browserPanel.revalidate()
                        browserPanel.repaint()

                        // 添加调试信息
                        println("Browser component added successfully")
                        println("Browser URL: ${browser?.cefBrowser?.url}")
                    } else {
                        throw Exception("Browser component is null")
                    }
                } catch (e: Exception) {
                    println("Error adding browser component: ${e.message}")
                    showFallbackUI(browserPanel)
                }
            }

            panel.add(browserPanel, BorderLayout.CENTER)

            // 添加说明文本
            val infoLabel = JBLabel("<html><body style='padding: 10px;'>" +
                    "请在上方浏览器中完成登录。如果页面显示为空白，请点击\"重新加载\"或使用外部浏览器登录。" +
                    "</body></html>")
            infoLabel.border = JBUI.Borders.empty(10)
            panel.add(infoLabel, BorderLayout.SOUTH)

        } catch (e: Exception) {
            println("JCEF initialization failed: ${e.message}")
            showFallbackUI(panel)
        }

        return panel
    }

    private fun showFallbackUI(panel: JPanel) {
        panel.removeAll()

        val errorPanel = JPanel()
        errorPanel.layout = BoxLayout(errorPanel, BoxLayout.Y_AXIS)
        errorPanel.border = JBUI.Borders.empty(20)

        val errorLabel = JBLabel("<html><body style='text-align: center;'>" +
                "<h3>内嵌浏览器不可用</h3>" +
                "<p>您的IDE版本可能不支持内嵌浏览器功能，或者JCEF组件未正确初始化。</p>" +
                "<p>请选择以下替代方案：</p>" +
                "</body></html>")
        errorLabel.horizontalAlignment = SwingConstants.CENTER
        errorPanel.add(errorLabel)

        errorPanel.add(Box.createVerticalStrut(20))

        // 添加外部浏览器按钮
        val openBrowserButton = JButton("在外部浏览器中打开")
        openBrowserButton.addActionListener {
            try {
                com.intellij.ide.BrowserUtil.browse(initialUrl)
                JOptionPane.showMessageDialog(
                    panel,
                    "已在外部浏览器中打开禅道登录页面。\n请在浏览器中完成登录，然后点击\"确认登录\"。",
                    "外部浏览器",
                    JOptionPane.INFORMATION_MESSAGE
                )
            } catch (e: Exception) {
                JOptionPane.showMessageDialog(
                    panel,
                    "无法打开浏览器: ${e.message}",
                    "错误",
                    JOptionPane.ERROR_MESSAGE
                )
            }
        }
        openBrowserButton.alignmentX = JComponent.CENTER_ALIGNMENT
        errorPanel.add(openBrowserButton)

        panel.add(errorPanel, BorderLayout.CENTER)
        panel.revalidate()
        panel.repaint()
    }

    private fun extractCookiesAndClose() {
        if (isLoginDetected) return
        isLoginDetected = true

        try {
            // 尝试从内嵌浏览器提取Cookie
            browser?.cefBrowser?.executeJavaScript(
                """
                // 提取所有Cookie
                const cookies = {};
                document.cookie.split(';').forEach(cookie => {
                    const [name, value] = cookie.trim().split('=');
                    if (name && value) {
                        cookies[name] = value;
                    }
                });

                // 将Cookie信息写入控制台，便于调试
                console.log('Current URL:', window.location.href);
                console.log('All cookies:', cookies);
                console.log('Important cookies:', {
                    PHPSESSID: cookies.PHPSESSID,
                    zentaosid: cookies.zentaosid,
                    za: cookies.za,
                    zp: cookies.zp
                });
                """.trimIndent(),
                browser?.cefBrowser?.url ?: "", 0
            )

            // 由于无法直接从JavaScript获取返回值，我们使用一个标记表示用户已确认登录
            extractedCookies["user_confirmed_login"] = "true"

        } catch (e: Exception) {
            println("Error extracting cookies: ${e.message}")
            extractedCookies["user_confirmed_login"] = "true"
        }

        SwingUtilities.invokeLater {
            onLoginSuccess(extractedCookies)
            close(OK_EXIT_CODE)
        }
    }
    
    override fun createActions(): Array<Action> {
        val manualConfirmAction = object : AbstractAction("确认登录") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                extractCookiesAndClose()
            }
        }

        val reloadAction = object : AbstractAction("重新加载") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                reloadBrowser()
            }
        }

        val openExternalAction = object : AbstractAction("外部浏览器") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                openInExternalBrowser()
            }
        }

        return arrayOf(manualConfirmAction, reloadAction, openExternalAction, cancelAction)
    }

    private fun reloadBrowser() {
        try {
            browser?.cefBrowser?.reload()
            println("Browser reloaded")
        } catch (e: Exception) {
            println("Error reloading browser: ${e.message}")
            JOptionPane.showMessageDialog(
                this.contentPane,
                "重新加载失败: ${e.message}",
                "错误",
                JOptionPane.ERROR_MESSAGE
            )
        }
    }

    private fun openInExternalBrowser() {
        try {
            com.intellij.ide.BrowserUtil.browse(initialUrl)
            JOptionPane.showMessageDialog(
                this.contentPane,
                "已在外部浏览器中打开禅道登录页面。\n请在浏览器中完成登录，然后点击\"确认登录\"。",
                "外部浏览器",
                JOptionPane.INFORMATION_MESSAGE
            )
        } catch (e: Exception) {
            JOptionPane.showMessageDialog(
                this.contentPane,
                "无法打开外部浏览器: ${e.message}",
                "错误",
                JOptionPane.ERROR_MESSAGE
            )
        }
    }
    
    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }
    
    override fun getPreferredSize(): Dimension {
        return Dimension(1200, 800)
    }
    
    override fun dispose() {
        browser?.dispose()
        super.dispose()
    }
}
