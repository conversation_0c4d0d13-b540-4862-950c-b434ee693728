package qhyu.asia.swcares.ui

import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.jcef.JBCefBrowser
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.*

class EmbeddedBrowserDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private var browser: JBCefBrowser? = null
    private var isLoginDetected = false
    private val extractedCookies = mutableMapOf<String, String>()

    init {
        title = "禅道登录 - 内嵌浏览器"
        init()
        setSize(1200, 800)
        setResizable(true)
    }
    
    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        try {
            // 创建内嵌浏览器
            browser = JBCefBrowser(initialUrl)

            panel.add(browser?.component, BorderLayout.CENTER)

            // 添加说明文本
            val infoLabel = JBLabel("<html><body style='padding: 10px;'>" +
                    "请在上方浏览器中完成登录。登录成功后，点击下方的\"确认登录\"按钮。" +
                    "</body></html>")
            infoLabel.border = JBUI.Borders.empty(10)
            panel.add(infoLabel, BorderLayout.SOUTH)
            
        } catch (e: Exception) {
            // 如果JCEF不可用，显示错误信息
            val errorLabel = JBLabel("<html><body style='padding: 20px; text-align: center;'>" +
                    "<h3>内嵌浏览器不可用</h3>" +
                    "<p>您的IDE版本可能不支持内嵌浏览器功能。</p>" +
                    "<p>请使用外部浏览器登录选项。</p>" +
                    "<p>错误信息: ${e.message}</p>" +
                    "</body></html>")
            errorLabel.horizontalAlignment = SwingConstants.CENTER
            panel.add(errorLabel, BorderLayout.CENTER)
        }

        return panel
    }

    private fun extractCookiesAndClose() {
        if (isLoginDetected) return
        isLoginDetected = true

        try {
            // 尝试从内嵌浏览器提取Cookie
            browser?.cefBrowser?.executeJavaScript(
                """
                // 提取所有Cookie
                const cookies = {};
                document.cookie.split(';').forEach(cookie => {
                    const [name, value] = cookie.trim().split('=');
                    if (name && value) {
                        cookies[name] = value;
                    }
                });

                // 将Cookie信息写入控制台，便于调试
                console.log('Current URL:', window.location.href);
                console.log('All cookies:', cookies);
                console.log('Important cookies:', {
                    PHPSESSID: cookies.PHPSESSID,
                    zentaosid: cookies.zentaosid,
                    za: cookies.za,
                    zp: cookies.zp
                });
                """.trimIndent(),
                browser?.cefBrowser?.url ?: "", 0
            )

            // 由于无法直接从JavaScript获取返回值，我们使用一个标记表示用户已确认登录
            extractedCookies["user_confirmed_login"] = "true"

        } catch (e: Exception) {
            println("Error extracting cookies: ${e.message}")
            extractedCookies["user_confirmed_login"] = "true"
        }

        SwingUtilities.invokeLater {
            onLoginSuccess(extractedCookies)
            close(OK_EXIT_CODE)
        }
    }
    
    override fun createActions(): Array<Action> {
        val manualConfirmAction = object : AbstractAction("手动确认登录") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                extractCookiesAndClose()
            }
        }
        
        return arrayOf(manualConfirmAction, cancelAction)
    }
    
    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }
    
    override fun getPreferredSize(): Dimension {
        return Dimension(1200, 800)
    }
    
    override fun dispose() {
        browser?.dispose()
        super.dispose()
    }
}
