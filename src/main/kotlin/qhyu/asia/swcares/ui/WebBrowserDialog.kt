package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBTextField
import com.intellij.ui.jcef.JBCefBrowser
import com.intellij.ui.jcef.JBCefBrowserBase
import com.intellij.ui.jcef.JBCefJSQuery
import com.intellij.util.ui.JBUI
import org.cef.browser.CefBrowser
import org.cef.browser.CefFrame
import org.cef.handler.CefLoadHandler
import org.cef.handler.CefRequestHandler
import org.cef.network.CefRequest
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.Desktop
import java.net.URI
import javax.swing.*

class WebBrowserDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private val sessionIdField = JBTextField()
    private val userTokenField = JBTextField()

    init {
        title = "禅道登录"
        init()
        setSize(600, 400)
        setResizable(true)

        // 自动打开浏览器
        openBrowserForLogin()
    }
    
    private fun openBrowserForLogin() {
        try {
            BrowserUtil.browse(initialUrl)
        } catch (e: Exception) {
            // 如果无法打开浏览器，显示错误
            SwingUtilities.invokeLater {
                JOptionPane.showMessageDialog(
                    null,
                    "无法打开浏览器，请手动访问: $initialUrl",
                    "提示",
                    JOptionPane.INFORMATION_MESSAGE
                )
            }
        }
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        // 创建说明面板
        val instructionPanel = JPanel()
        instructionPanel.layout = BoxLayout(instructionPanel, BoxLayout.Y_AXIS)
        instructionPanel.border = JBUI.Borders.empty(20)

        val titleLabel = JBLabel("<html><h2>禅道登录助手</h2></html>")
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(titleLabel)

        instructionPanel.add(Box.createVerticalStrut(10))

        val step1Label = JBLabel("<html><b>步骤 1:</b> 浏览器已自动打开禅道登录页面</html>")
        instructionPanel.add(step1Label)

        instructionPanel.add(Box.createVerticalStrut(5))

        val step2Label = JBLabel("<html><b>步骤 2:</b> 在浏览器中完成登录</html>")
        instructionPanel.add(step2Label)

        instructionPanel.add(Box.createVerticalStrut(5))

        val step3Label = JBLabel("<html><b>步骤 3:</b> 登录成功后，点击下方的\"确认登录\"按钮</html>")
        instructionPanel.add(step3Label)

        instructionPanel.add(Box.createVerticalStrut(20))

        // 添加手动输入选项（高级用户）
        val advancedPanel = JPanel()
        advancedPanel.layout = BoxLayout(advancedPanel, BoxLayout.Y_AXIS)
        advancedPanel.border = JBUI.Borders.compound(
            JBUI.Borders.customLine(java.awt.Color.GRAY, 1, 0, 0, 0),
            JBUI.Borders.empty(10, 0, 0, 0)
        )

        val advancedLabel = JBLabel("<html><b>高级选项（可选）:</b></html>")
        advancedPanel.add(advancedLabel)

        advancedPanel.add(Box.createVerticalStrut(10))

        val sessionPanel = JPanel(BorderLayout())
        sessionPanel.add(JBLabel("Session ID: "), BorderLayout.WEST)
        sessionIdField.toolTipText = "如果您知道会话ID，可以直接输入"
        sessionPanel.add(sessionIdField, BorderLayout.CENTER)
        advancedPanel.add(sessionPanel)

        advancedPanel.add(Box.createVerticalStrut(5))

        val tokenPanel = JPanel(BorderLayout())
        tokenPanel.add(JBLabel("用户令牌: "), BorderLayout.WEST)
        userTokenField.toolTipText = "如果您知道用户令牌，可以直接输入"
        tokenPanel.add(userTokenField, BorderLayout.CENTER)
        advancedPanel.add(tokenPanel)

        instructionPanel.add(advancedPanel)

        panel.add(instructionPanel, BorderLayout.CENTER)

        // 添加底部按钮说明
        val bottomPanel = JPanel(BorderLayout())
        val tipLabel = JBLabel("<html><i>提示: 如果浏览器没有自动打开，请手动访问: $initialUrl</i></html>")
        tipLabel.border = JBUI.Borders.empty(10)
        bottomPanel.add(tipLabel, BorderLayout.CENTER)

        val reopenButton = JButton("重新打开浏览器")
        reopenButton.addActionListener { openBrowserForLogin() }
        bottomPanel.add(reopenButton, BorderLayout.EAST)

        panel.add(bottomPanel, BorderLayout.SOUTH)

        return panel
    }
    
    private fun handleLoginConfirmation() {
        // 创建模拟的 cookies（实际应用中可能需要更复杂的逻辑）
        val cookies = mutableMapOf<String, String>()

        // 如果用户输入了会话信息，使用它们
        val sessionId = sessionIdField.text.trim()
        val userToken = userTokenField.text.trim()

        if (sessionId.isNotEmpty()) {
            cookies["PHPSESSID"] = sessionId
        }
        if (userToken.isNotEmpty()) {
            cookies["zentaosid"] = userToken
        }

        // 如果没有输入具体信息，创建一个标记表示用户已确认登录
        if (cookies.isEmpty()) {
            cookies["user_confirmed_login"] = "true"
        }

        // 调用成功回调
        onLoginSuccess(cookies)
    }
    
    override fun createActions(): Array<Action> {
        val confirmAction = object : AbstractAction("确认登录") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                handleLoginConfirmation()
                close(OK_EXIT_CODE)
            }
        }

        return arrayOf(confirmAction, cancelAction)
    }

    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }

    override fun getPreferredSize(): Dimension {
        return Dimension(600, 400)
    }
}
