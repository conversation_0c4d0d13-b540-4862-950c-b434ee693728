package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBTextField
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.Desktop
import java.net.URI
import javax.swing.*

class WebBrowserDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private val sessionIdField = JBTextField()
    private val userTokenField = JBTextField()

    init {
        title = "禅道登录"
        init()
        setSize(600, 400)
        setResizable(true)

        // 自动打开浏览器
        openBrowserForLogin()
    }
    
    private fun openBrowserForLogin() {
        try {
            BrowserUtil.browse(initialUrl)
        } catch (e: Exception) {
            // 如果无法打开浏览器，显示错误
            SwingUtilities.invokeLater {
                JOptionPane.showMessageDialog(
                    null,
                    "无法打开浏览器，请手动访问: $initialUrl",
                    "提示",
                    JOptionPane.INFORMATION_MESSAGE
                )
            }
        }
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        // 创建说明面板
        val instructionPanel = JPanel()
        instructionPanel.layout = BoxLayout(instructionPanel, BoxLayout.Y_AXIS)
        instructionPanel.border = JBUI.Borders.empty(20)

        val titleLabel = JBLabel("<html><h2>禅道登录助手</h2></html>")
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(titleLabel)

        instructionPanel.add(Box.createVerticalStrut(10))

        val step1Label = JBLabel("<html><b>步骤 1:</b> 浏览器已自动打开禅道登录页面</html>")
        instructionPanel.add(step1Label)

        instructionPanel.add(Box.createVerticalStrut(5))

        val step2Label = JBLabel("<html><b>步骤 2:</b> 在浏览器中完成登录</html>")
        instructionPanel.add(step2Label)

        instructionPanel.add(Box.createVerticalStrut(5))

        val step3Label = JBLabel("<html><b>步骤 3:</b> 登录成功后，点击下方的\"确认登录\"按钮</html>")
        instructionPanel.add(step3Label)

        instructionPanel.add(Box.createVerticalStrut(20))

        // 添加手动输入选项（高级用户）
        val advancedPanel = JPanel()
        advancedPanel.layout = BoxLayout(advancedPanel, BoxLayout.Y_AXIS)
        advancedPanel.border = JBUI.Borders.compound(
            JBUI.Borders.customLine(java.awt.Color.GRAY, 1, 0, 0, 0),
            JBUI.Borders.empty(10, 0, 0, 0)
        )

        val advancedLabel = JBLabel("<html><b>高级选项（可选）:</b></html>")
        advancedPanel.add(advancedLabel)

        advancedPanel.add(Box.createVerticalStrut(10))

        // Cookie 输入说明
        val cookieInstructionLabel = JBLabel("<html><i>如果浏览器已登录，请按F12打开开发者工具，在Application/Storage → Cookies中找到以下信息：</i></html>")
        advancedPanel.add(cookieInstructionLabel)

        advancedPanel.add(Box.createVerticalStrut(5))

        val sessionPanel = JPanel(BorderLayout())
        sessionPanel.add(JBLabel("PHPSESSID: "), BorderLayout.WEST)
        sessionIdField.toolTipText = "从浏览器Cookie中复制PHPSESSID的值"
        sessionPanel.add(sessionIdField, BorderLayout.CENTER)
        advancedPanel.add(sessionPanel)

        advancedPanel.add(Box.createVerticalStrut(5))

        val tokenPanel = JPanel(BorderLayout())
        tokenPanel.add(JBLabel("zentaosid: "), BorderLayout.WEST)
        userTokenField.toolTipText = "从浏览器Cookie中复制zentaosid的值（如果有）"
        tokenPanel.add(userTokenField, BorderLayout.CENTER)
        advancedPanel.add(tokenPanel)

        advancedPanel.add(Box.createVerticalStrut(10))

        // 添加Cookie获取说明按钮
        val helpButton = JButton("如何获取Cookie?")
        helpButton.addActionListener { showCookieHelp() }
        advancedPanel.add(helpButton)

        instructionPanel.add(advancedPanel)

        panel.add(instructionPanel, BorderLayout.CENTER)

        // 添加底部按钮说明
        val bottomPanel = JPanel(BorderLayout())
        val tipLabel = JBLabel("<html><i>提示: 如果浏览器没有自动打开，请手动访问: $initialUrl</i></html>")
        tipLabel.border = JBUI.Borders.empty(10)
        bottomPanel.add(tipLabel, BorderLayout.CENTER)

        val reopenButton = JButton("重新打开浏览器")
        reopenButton.addActionListener { openBrowserForLogin() }
        bottomPanel.add(reopenButton, BorderLayout.EAST)

        panel.add(bottomPanel, BorderLayout.SOUTH)

        return panel
    }
    
    private fun showCookieHelp() {
        val helpMessage = """
            <html>
            <h3>如何从浏览器获取Cookie：</h3>
            <ol>
            <li><b>打开开发者工具</b>：在已登录的禅道页面按 F12</li>
            <li><b>找到Application标签</b>：点击顶部的 "Application" 或 "应用程序"</li>
            <li><b>展开Storage</b>：在左侧找到 "Storage" → "Cookies"</li>
            <li><b>选择网站</b>：点击 "https://chandao.sw"</li>
            <li><b>复制Cookie值</b>：
                <ul>
                <li>找到 <code>PHPSESSID</code>，复制其 Value 值</li>
                <li>找到 <code>zentaosid</code>（如果有），复制其 Value 值</li>
                </ul>
            </li>
            <li><b>粘贴到插件</b>：将复制的值粘贴到对应的输入框中</li>
            </ol>
            <p><i>注意：Cookie值通常是一长串字母数字组合</i></p>
            </html>
        """.trimIndent()

        JOptionPane.showMessageDialog(
            this.contentPane,
            helpMessage,
            "Cookie获取帮助",
            JOptionPane.INFORMATION_MESSAGE
        )
    }

    private fun handleLoginConfirmation() {
        val cookies = mutableMapOf<String, String>()

        // 如果用户输入了会话信息，使用它们
        val sessionId = sessionIdField.text.trim()
        val userToken = userTokenField.text.trim()

        if (sessionId.isNotEmpty()) {
            cookies["PHPSESSID"] = sessionId
        }
        if (userToken.isNotEmpty()) {
            cookies["zentaosid"] = userToken
        }

        // 如果没有输入具体信息，创建一个标记表示用户已确认登录
        if (cookies.isEmpty()) {
            cookies["user_confirmed_login"] = "true"
        }

        // 调用成功回调
        onLoginSuccess(cookies)
    }
    
    override fun createActions(): Array<Action> {
        val confirmAction = object : AbstractAction("确认登录") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                handleLoginConfirmation()
                close(OK_EXIT_CODE)
            }
        }

        return arrayOf(confirmAction, cancelAction)
    }

    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }

    override fun getPreferredSize(): Dimension {
        return Dimension(600, 400)
    }
}
