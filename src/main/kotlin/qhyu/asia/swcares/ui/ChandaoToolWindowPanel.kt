package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.Messages
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.table.JBTable
import com.intellij.util.ui.JBUI
import kotlinx.coroutines.runBlocking
import qhyu.asia.swcares.model.ChandaoRecord
import qhyu.asia.swcares.service.ChandaoWebService
import java.awt.BorderLayout
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import javax.swing.*
import javax.swing.table.AbstractTableModel

class ChandaoToolWindowPanel(private val project: Project) : JPanel(BorderLayout()) {
    
    private val logger = thisLogger()
    private val webService = ApplicationManager.getApplication().service<ChandaoWebService>()
    
    private val tableModel = ChandaoTableModel()
    private val table = JBTable(tableModel)
    private val statusLabel = JBLabel("就绪")
    
    init {
        setupUI()
        setupTableActions()
    }
    
    private fun setupUI() {
        // 创建工具栏
        val toolbar = createToolbar()
        add(toolbar, BorderLayout.NORTH)
        
        // 创建表格
        setupTable()
        val scrollPane = JBScrollPane(table)
        add(scrollPane, BorderLayout.CENTER)
        
        // 创建状态栏
        val statusPanel = JPanel(BorderLayout())
        statusPanel.border = JBUI.Borders.empty(5)
        statusPanel.add(statusLabel, BorderLayout.WEST)
        add(statusPanel, BorderLayout.SOUTH)
        
        // 初始状态
        updateStatus("点击刷新按钮获取数据")
    }
    
    private fun createToolbar(): JComponent {
        val toolbar = JPanel()
        toolbar.layout = BoxLayout(toolbar, BoxLayout.X_AXIS)
        toolbar.border = JBUI.Borders.empty(5)
        
        // 刷新按钮
        val refreshButton = JButton("刷新数据")
        refreshButton.addActionListener { refreshData() }
        toolbar.add(refreshButton)
        
        toolbar.add(Box.createHorizontalStrut(10))
        
        // 打开浏览器按钮
        val browserButton = JButton("在浏览器中打开")
        browserButton.addActionListener { openInBrowser() }
        toolbar.add(browserButton)
        
        toolbar.add(Box.createHorizontalStrut(10))
        
        // 登出按钮
        val logoutButton = JButton("登出")
        logoutButton.addActionListener { logout() }
        toolbar.add(logoutButton)
        
        toolbar.add(Box.createHorizontalGlue())
        
        return toolbar
    }
    
    private fun setupTable() {
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION)
        table.autoResizeMode = JTable.AUTO_RESIZE_ALL_COLUMNS
        
        // 设置列宽
        val columnModel = table.columnModel
        columnModel.getColumn(0).preferredWidth = 60  // ID
        columnModel.getColumn(1).preferredWidth = 200 // 标题
        columnModel.getColumn(2).preferredWidth = 80  // 类型
        columnModel.getColumn(3).preferredWidth = 80  // 状态
        columnModel.getColumn(4).preferredWidth = 60  // 优先级
        columnModel.getColumn(5).preferredWidth = 100 // 指派给
    }
    
    private fun setupTableActions() {
        // 双击打开链接
        table.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                if (e.clickCount == 2) {
                    val selectedRow = table.selectedRow
                    if (selectedRow >= 0) {
                        val record = tableModel.getRecordAt(selectedRow)
                        if (record.url.isNotEmpty()) {
                            BrowserUtil.browse(record.url)
                        }
                    }
                }
            }
        })
        
        // 右键菜单
        val popupMenu = JPopupMenu()
        val openUrlAction = JMenuItem("在浏览器中打开")
        openUrlAction.addActionListener {
            val selectedRow = table.selectedRow
            if (selectedRow >= 0) {
                val record = tableModel.getRecordAt(selectedRow)
                if (record.url.isNotEmpty()) {
                    BrowserUtil.browse(record.url)
                }
            }
        }
        popupMenu.add(openUrlAction)
        
        val copyUrlAction = JMenuItem("复制链接")
        copyUrlAction.addActionListener {
            val selectedRow = table.selectedRow
            if (selectedRow >= 0) {
                val record = tableModel.getRecordAt(selectedRow)
                if (record.url.isNotEmpty()) {
                    val clipboard = java.awt.Toolkit.getDefaultToolkit().systemClipboard
                    val stringSelection = java.awt.datatransfer.StringSelection(record.url)
                    clipboard.setContents(stringSelection, null)
                    updateStatus("链接已复制到剪贴板")
                }
            }
        }
        popupMenu.add(copyUrlAction)
        
        table.componentPopupMenu = popupMenu
    }
    
    private fun refreshData() {
        updateStatus("正在刷新数据...")
        
        SwingUtilities.invokeLater {
            try {
                runBlocking {
                    val result = webService.crawlMyPageData()
                    SwingUtilities.invokeLater {
                        if (result.success) {
                            updateData(result.records)
                            updateStatus("数据刷新完成，共 ${result.records.size} 条记录")
                        } else {
                            updateStatus("刷新失败: ${result.message}")
                            Messages.showErrorDialog(project, result.message, "刷新失败")
                        }
                    }
                }
            } catch (e: Exception) {
                logger.error("Error refreshing data", e)
                SwingUtilities.invokeLater {
                    updateStatus("刷新失败: ${e.message}")
                    Messages.showErrorDialog(project, "刷新数据时发生错误: ${e.message}", "错误")
                }
            }
        }
    }
    
    private fun openInBrowser() {
        val config = webService.getConfig()
        val url = "${config.baseUrl}${config.myPath}"
        BrowserUtil.browse(url)
        updateStatus("已在浏览器中打开禅道页面")
    }
    
    private fun logout() {
        val result = Messages.showYesNoDialog(
            project,
            "确定要登出吗？",
            "确认登出",
            Messages.getQuestionIcon()
        )
        
        if (result == Messages.YES) {
            try {
                runBlocking {
                    webService.logout()
                }
                tableModel.clearData()
                updateStatus("已登出")
                Messages.showInfoMessage(project, "已成功登出", "登出")
            } catch (e: Exception) {
                logger.error("Error during logout", e)
                Messages.showErrorDialog(project, "登出时发生错误: ${e.message}", "错误")
            }
        }
    }
    
    fun updateData(records: List<ChandaoRecord>) {
        tableModel.updateData(records)
    }
    
    private fun updateStatus(message: String) {
        statusLabel.text = message
    }
    
    private class ChandaoTableModel : AbstractTableModel() {
        private val columnNames = arrayOf("ID", "标题", "类型", "状态", "优先级", "指派给")
        private var records = listOf<ChandaoRecord>()
        
        override fun getRowCount(): Int = records.size
        
        override fun getColumnCount(): Int = columnNames.size
        
        override fun getColumnName(column: Int): String = columnNames[column]
        
        override fun getValueAt(rowIndex: Int, columnIndex: Int): Any {
            val record = records[rowIndex]
            return when (columnIndex) {
                0 -> record.id
                1 -> record.title
                2 -> record.type
                3 -> record.status
                4 -> record.priority
                5 -> record.assignedTo
                else -> ""
            }
        }
        
        fun updateData(newRecords: List<ChandaoRecord>) {
            records = newRecords
            fireTableDataChanged()
        }
        
        fun clearData() {
            records = emptyList()
            fireTableDataChanged()
        }
        
        fun getRecordAt(rowIndex: Int): ChandaoRecord {
            return records[rowIndex]
        }
    }
}
