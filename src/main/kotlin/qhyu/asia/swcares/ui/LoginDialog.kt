package qhyu.asia.swcares.ui

import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPasswordField
import com.intellij.ui.components.JBTextField
import com.intellij.util.ui.FormBuilder
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.*

class LoginDialog(project: Project?) : DialogWrapper(project) {
    
    private val usernameField = JBTextField()
    private val passwordField = JBPasswordField()
    private val rememberCheckBox = JCheckBox("记住用户名")
    
    init {
        title = "禅道登录"
        init()
        
        // 设置对话框大小
        setSize(400, 200)
        
        // 设置默认焦点
        usernameField.requestFocusInWindow()
    }
    
    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        
        // 创建表单
        val formPanel = FormBuilder.createFormBuilder()
            .addLabeledComponent(JBLabel("用户名:"), usernameField, 1, false)
            .addLabeledComponent(JBLabel("密码:"), passwordField, 1, false)
            .addComponent(rememberCheckBox, 1)
            .addComponentFillVertically(JPanel(), 0)
            .panel
        
        // 设置边距
        formPanel.border = JBUI.Borders.empty(10)
        
        panel.add(formPanel, BorderLayout.CENTER)
        
        // 添加说明文本
        val infoLabel = JBLabel("<html><body style='width: 350px'>" +
                "请输入您的禅道账号信息。<br>" +
                "如果您更喜欢在浏览器中登录，请取消此对话框并选择浏览器登录选项。" +
                "</body></html>")
        infoLabel.border = JBUI.Borders.empty(0, 10, 10, 10)
        panel.add(infoLabel, BorderLayout.NORTH)
        
        return panel
    }
    
    override fun getPreferredFocusedComponent(): JComponent {
        return usernameField
    }
    
    override fun doValidate(): ValidationInfo? {
        if (usernameField.text.trim().isEmpty()) {
            return ValidationInfo("请输入用户名", usernameField)
        }
        
        if (passwordField.password.isEmpty()) {
            return ValidationInfo("请输入密码", passwordField)
        }
        
        return null
    }
    
    fun getUsername(): String {
        return usernameField.text.trim()
    }
    
    fun getPassword(): String {
        return String(passwordField.password)
    }
    
    fun isRememberUsername(): Boolean {
        return rememberCheckBox.isSelected
    }
    
    override fun createActions(): Array<Action> {
        return arrayOf(okAction, cancelAction)
    }
    
    override fun getOKAction(): Action {
        val action = super.getOKAction()
        action.putValue(Action.NAME, "登录")
        return action
    }
    
    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }
}
