package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBTextArea
import com.intellij.ui.components.JBTextField
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.Font
import java.awt.Toolkit
import java.awt.datatransfer.StringSelection
import javax.swing.*

/**
 * 智能登录助手 - 最可靠的登录方案
 */
class SmartLoginDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {
    
    private val cookieField = JBTextArea(3, 50)
    private var step = 1
    
    init {
        title = "禅道智能登录助手"
        init()
        setSize(700, 500)
        setResizable(true)
        
        // 自动打开浏览器
        openBrowserAndShowInstructions()
    }
    
    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        
        // 创建主面板
        val mainPanel = JPanel()
        mainPanel.layout = BoxLayout(mainPanel, BoxLayout.Y_AXIS)
        mainPanel.border = JBUI.Borders.empty(20)
        
        // 标题
        val titleLabel = JBLabel("<html><h2>🚀 禅道智能登录助手</h2></html>")
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        mainPanel.add(titleLabel)
        
        mainPanel.add(Box.createVerticalStrut(20))
        
        // 步骤说明
        val stepsPanel = createStepsPanel()
        mainPanel.add(stepsPanel)
        
        mainPanel.add(Box.createVerticalStrut(20))
        
        // Cookie输入区域
        val cookiePanel = createCookiePanel()
        mainPanel.add(cookiePanel)
        
        mainPanel.add(Box.createVerticalStrut(20))
        
        // 按钮区域
        val buttonPanel = createButtonPanel()
        mainPanel.add(buttonPanel)
        
        panel.add(mainPanel, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createStepsPanel(): JPanel {
        val panel = JPanel()
        panel.layout = BoxLayout(panel, BoxLayout.Y_AXIS)
        panel.border = JBUI.Borders.compound(
            JBUI.Borders.customLine(java.awt.Color.LIGHT_GRAY),
            JBUI.Borders.empty(15)
        )
        
        val stepLabels = listOf(
            "✅ 步骤1: 浏览器已自动打开禅道登录页面",
            "🔑 步骤2: 在浏览器中输入用户名和密码完成登录",
            "🛠️ 步骤3: 按 F12 打开开发者工具",
            "📋 步骤4: 点击 Console（控制台）标签",
            "⚡ 步骤5: 复制下方脚本到控制台并按回车",
            "📄 步骤6: 复制输出结果到下方文本框",
            "🎯 步骤7: 点击\"确认登录\"完成"
        )
        
        stepLabels.forEach { stepText ->
            val label = JBLabel("<html>$stepText</html>")
            label.alignmentX = JComponent.LEFT_ALIGNMENT
            panel.add(label)
            panel.add(Box.createVerticalStrut(8))
        }
        
        return panel
    }
    
    private fun createCookiePanel(): JPanel {
        val panel = JPanel(BorderLayout())
        
        // 脚本区域
        val scriptLabel = JBLabel("<html><b>🔧 Cookie提取脚本（复制到浏览器控制台）:</b></html>")
        panel.add(scriptLabel, BorderLayout.NORTH)
        
        val scriptArea = JBTextArea(4, 50)
        scriptArea.text = """
// 禅道Cookie提取脚本 - 复制此脚本到浏览器控制台运行
(function() {
    const cookies = document.cookie.split(';').reduce((acc, cookie) => {
        const [name, value] = cookie.trim().split('=');
        if (name && value && ['PHPSESSID', 'zentaosid', 'za', 'zp'].includes(name)) {
            acc[name] = value;
        }
        return acc;
    }, {});
    
    const result = Object.entries(cookies).map(([k,v]) => k + '=' + v).join('; ');
    console.log('🍪 提取的Cookie:', result);
    console.log('📋 请复制上面的Cookie字符串到插件中');
    return result;
})();
        """.trimIndent()
        
        scriptArea.isEditable = false
        scriptArea.font = Font(Font.MONOSPACED, Font.PLAIN, 12)
        scriptArea.background = java.awt.Color(245, 245, 245)
        scriptArea.border = JBUI.Borders.compound(
            JBUI.Borders.customLine(java.awt.Color.GRAY),
            JBUI.Borders.empty(5)
        )
        
        val scriptScrollPane = JScrollPane(scriptArea)
        scriptScrollPane.preferredSize = Dimension(600, 120)
        
        val scriptPanel = JPanel(BorderLayout())
        scriptPanel.add(scriptScrollPane, BorderLayout.CENTER)
        
        // 复制脚本按钮
        val copyScriptButton = JButton("📋 复制脚本")
        copyScriptButton.addActionListener {
            val clipboard = Toolkit.getDefaultToolkit().systemClipboard
            clipboard.setContents(StringSelection(scriptArea.text), null)
            JOptionPane.showMessageDialog(this.contentPane, "脚本已复制到剪贴板！", "成功", JOptionPane.INFORMATION_MESSAGE)
        }
        scriptPanel.add(copyScriptButton, BorderLayout.SOUTH)
        
        panel.add(scriptPanel, BorderLayout.CENTER)
        
        // Cookie输入区域
        val cookieInputPanel = JPanel(BorderLayout())
        cookieInputPanel.border = JBUI.Borders.empty(15, 0, 0, 0)
        
        val cookieLabel = JBLabel("<html><b>🍪 粘贴Cookie结果:</b></html>")
        cookieInputPanel.add(cookieLabel, BorderLayout.NORTH)
        
        cookieField.toolTipText = "将浏览器控制台的输出结果粘贴到这里"
        cookieField.border = JBUI.Borders.compound(
            JBUI.Borders.customLine(java.awt.Color.GRAY),
            JBUI.Borders.empty(5)
        )
        
        val cookieScrollPane = JScrollPane(cookieField)
        cookieScrollPane.preferredSize = Dimension(600, 80)
        cookieInputPanel.add(cookieScrollPane, BorderLayout.CENTER)
        
        panel.add(cookieInputPanel, BorderLayout.SOUTH)
        
        return panel
    }
    
    private fun createButtonPanel(): JPanel {
        val panel = JPanel()
        panel.layout = BoxLayout(panel, BoxLayout.X_AXIS)
        
        val reopenButton = JButton("🔄 重新打开浏览器")
        reopenButton.addActionListener { openBrowserAndShowInstructions() }
        panel.add(reopenButton)
        
        panel.add(Box.createHorizontalStrut(10))
        
        val helpButton = JButton("❓ 详细帮助")
        helpButton.addActionListener { showDetailedHelp() }
        panel.add(helpButton)
        
        panel.add(Box.createHorizontalGlue())
        
        return panel
    }
    
    private fun openBrowserAndShowInstructions() {
        try {
            BrowserUtil.browse(initialUrl)
            step = 2
        } catch (e: Exception) {
            JOptionPane.showMessageDialog(
                this.contentPane,
                "无法自动打开浏览器，请手动访问:\n$initialUrl",
                "提示",
                JOptionPane.INFORMATION_MESSAGE
            )
        }
    }
    
    private fun showDetailedHelp() {
        val helpText = """
        <html>
        <h3>详细操作步骤：</h3>
        <ol>
        <li><b>打开浏览器</b>：点击"重新打开浏览器"或手动访问禅道网站</li>
        <li><b>登录账号</b>：在浏览器中输入用户名和密码</li>
        <li><b>打开开发者工具</b>：按F12键（或右键→检查元素）</li>
        <li><b>切换到控制台</b>：点击"Console"或"控制台"标签</li>
        <li><b>运行脚本</b>：
            <ul>
            <li>点击"复制脚本"按钮</li>
            <li>在控制台中粘贴脚本（Ctrl+V）</li>
            <li>按回车键运行</li>
            </ul>
        </li>
        <li><b>复制结果</b>：控制台会显示Cookie字符串，复制它</li>
        <li><b>粘贴到插件</b>：将Cookie字符串粘贴到下方文本框</li>
        <li><b>确认登录</b>：点击"确认登录"按钮</li>
        </ol>
        
        <p><b>注意事项：</b></p>
        <ul>
        <li>确保在禅道网站上已经成功登录</li>
        <li>Cookie字符串通常包含PHPSESSID等信息</li>
        <li>如果控制台没有输出，请检查是否正确粘贴了脚本</li>
        </ul>
        </html>
        """
        
        JOptionPane.showMessageDialog(
            this.contentPane,
            helpText,
            "详细帮助",
            JOptionPane.INFORMATION_MESSAGE
        )
    }
    
    override fun createActions(): Array<Action> {
        val confirmAction = object : AbstractAction("✅ 确认登录") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                handleLoginConfirmation()
            }
        }
        
        return arrayOf(confirmAction, cancelAction)
    }
    
    private fun handleLoginConfirmation() {
        val cookieText = cookieField.text.trim()
        val cookies = mutableMapOf<String, String>()
        
        if (cookieText.isNotEmpty()) {
            try {
                // 解析Cookie字符串
                cookieText.split(";").forEach { pair ->
                    val parts = pair.trim().split("=", limit = 2)
                    if (parts.size == 2) {
                        cookies[parts[0].trim()] = parts[1].trim()
                    }
                }
                
                if (cookies.isNotEmpty()) {
                    JOptionPane.showMessageDialog(
                        this.contentPane,
                        "成功解析到 ${cookies.size} 个Cookie！\n正在验证登录状态...",
                        "成功",
                        JOptionPane.INFORMATION_MESSAGE
                    )
                    onLoginSuccess(cookies)
                    close(OK_EXIT_CODE)
                    return
                }
            } catch (e: Exception) {
                println("Error parsing cookies: ${e.message}")
            }
        }
        
        // 如果没有Cookie，询问用户是否仍要继续
        val result = JOptionPane.showConfirmDialog(
            this.contentPane,
            "未检测到有效的Cookie。\n\n可能的原因：\n" +
                    "• 尚未在浏览器中完成登录\n" +
                    "• 脚本未正确运行\n" +
                    "• Cookie字符串格式不正确\n\n" +
                    "是否仍要尝试继续？",
            "确认",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE
        )
        
        if (result == JOptionPane.YES_OPTION) {
            cookies["user_confirmed_login"] = "true"
            onLoginSuccess(cookies)
            close(OK_EXIT_CODE)
        }
    }
    
    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "❌ 取消")
        return action
    }
    
    override fun getPreferredSize(): Dimension {
        return Dimension(700, 500)
    }
}
