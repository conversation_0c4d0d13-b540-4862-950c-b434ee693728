package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBTextField
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.*

/**
 * 简化版浏览器对话框，当JCEF不可用时使用
 */
class SimpleBrowserDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {
    
    private val cookieField = JBTextField()
    
    init {
        title = "禅道登录 - 简化版"
        init()
        setSize(600, 400)
        setResizable(true)
        
        // 自动打开外部浏览器
        openExternalBrowser()
    }
    
    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        
        // 创建说明面板
        val instructionPanel = JPanel()
        instructionPanel.layout = BoxLayout(instructionPanel, BoxLayout.Y_AXIS)
        instructionPanel.border = JBUI.Borders.empty(20)
        
        val titleLabel = JBLabel("<html><h2>禅道登录助手</h2></html>")
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(titleLabel)
        
        instructionPanel.add(Box.createVerticalStrut(10))
        
        val step1Label = JBLabel("<html><b>步骤 1:</b> 外部浏览器已自动打开禅道登录页面</html>")
        instructionPanel.add(step1Label)
        
        instructionPanel.add(Box.createVerticalStrut(5))
        
        val step2Label = JBLabel("<html><b>步骤 2:</b> 在浏览器中完成登录</html>")
        instructionPanel.add(step2Label)
        
        instructionPanel.add(Box.createVerticalStrut(5))
        
        val step3Label = JBLabel("<html><b>步骤 3:</b> 按F12打开开发者工具，在Console中运行以下脚本：</html>")
        instructionPanel.add(step3Label)
        
        instructionPanel.add(Box.createVerticalStrut(10))
        
        // 添加脚本文本区域
        val scriptArea = JTextArea(3, 50)
        scriptArea.text = "document.cookie.split(';').map(c => c.trim()).filter(c => c.includes('PHPSESSID') || c.includes('zentaosid')).join('; ')"
        scriptArea.isEditable = false
        scriptArea.background = panel.background
        scriptArea.border = JBUI.Borders.compound(
            JBUI.Borders.customLine(java.awt.Color.GRAY),
            JBUI.Borders.empty(5)
        )
        val scriptScrollPane = JScrollPane(scriptArea)
        scriptScrollPane.preferredSize = Dimension(500, 60)
        instructionPanel.add(scriptScrollPane)
        
        instructionPanel.add(Box.createVerticalStrut(10))
        
        val step4Label = JBLabel("<html><b>步骤 4:</b> 复制脚本输出结果，粘贴到下方输入框：</html>")
        instructionPanel.add(step4Label)
        
        instructionPanel.add(Box.createVerticalStrut(10))
        
        // Cookie输入框
        val cookiePanel = JPanel(BorderLayout())
        cookiePanel.add(JBLabel("Cookie: "), BorderLayout.WEST)
        cookieField.toolTipText = "粘贴从浏览器控制台获取的Cookie字符串"
        cookiePanel.add(cookieField, BorderLayout.CENTER)
        instructionPanel.add(cookiePanel)
        
        instructionPanel.add(Box.createVerticalStrut(20))
        
        // 添加重新打开浏览器按钮
        val reopenButton = JButton("重新打开浏览器")
        reopenButton.addActionListener { openExternalBrowser() }
        reopenButton.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(reopenButton)
        
        panel.add(instructionPanel, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun openExternalBrowser() {
        try {
            BrowserUtil.browse(initialUrl)
        } catch (e: Exception) {
            SwingUtilities.invokeLater {
                JOptionPane.showMessageDialog(
                    this.contentPane,
                    "无法打开浏览器，请手动访问: $initialUrl",
                    "提示",
                    JOptionPane.INFORMATION_MESSAGE
                )
            }
        }
    }
    
    override fun createActions(): Array<Action> {
        val confirmAction = object : AbstractAction("确认登录") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                handleLoginConfirmation()
            }
        }
        
        return arrayOf(confirmAction, cancelAction)
    }
    
    private fun handleLoginConfirmation() {
        val cookieString = cookieField.text.trim()
        val cookies = mutableMapOf<String, String>()
        
        if (cookieString.isNotEmpty()) {
            // 解析Cookie字符串
            try {
                cookieString.split(";").forEach { pair ->
                    val parts = pair.trim().split("=", limit = 2)
                    if (parts.size == 2) {
                        cookies[parts[0].trim()] = parts[1].trim()
                    }
                }
                
                if (cookies.isNotEmpty()) {
                    onLoginSuccess(cookies)
                    close(OK_EXIT_CODE)
                    return
                }
            } catch (e: Exception) {
                println("Error parsing cookies: ${e.message}")
            }
        }
        
        // 如果没有Cookie或解析失败，仍然允许用户确认
        val result = JOptionPane.showConfirmDialog(
            this.contentPane,
            "未检测到有效的Cookie。是否仍要继续？\n（插件将尝试使用当前浏览器状态）",
            "确认",
            JOptionPane.YES_NO_OPTION
        )
        
        if (result == JOptionPane.YES_OPTION) {
            cookies["user_confirmed_login"] = "true"
            onLoginSuccess(cookies)
            close(OK_EXIT_CODE)
        }
    }
    
    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }
    
    override fun getPreferredSize(): Dimension {
        return Dimension(600, 400)
    }
}
