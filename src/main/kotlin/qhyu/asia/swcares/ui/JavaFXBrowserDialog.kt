package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import kotlinx.coroutines.*
import okhttp3.*
import java.awt.BorderLayout
import java.awt.Desktop
import java.awt.Dimension
import java.io.File
import java.net.URI
import javax.swing.*

/**
 * 真正的内嵌浏览器 - 使用本地HTML文件和自动刷新
 */
class JavaFXBrowserDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private var htmlFile: File? = null
    private var refreshJob: Job? = null
    private var isLoginDetected = false
    private val client = OkHttpClient.Builder()
        .cookieJar(CookieJar.NO_COOKIES)
        .build()
    
    init {
        title = "禅道登录 - 内嵌浏览器"
        init()
        setSize(1200, 800)
        setResizable(true)

        // 创建本地HTML文件并打开浏览器
        createLocalHtmlAndOpenBrowser()
    }
    
    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        // 创建说明面板
        val instructionPanel = JPanel()
        instructionPanel.layout = BoxLayout(instructionPanel, BoxLayout.Y_AXIS)
        instructionPanel.border = JBUI.Borders.empty(20)

        val titleLabel = JBLabel("<html><h2>🌐 IDEA内嵌网页登录</h2></html>")
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(titleLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        val statusLabel = JBLabel("<html><b>状态：</b>浏览器已在外部打开，正在监控登录状态...</html>")
        statusLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(statusLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        val stepsLabel = JBLabel("<html>" +
                "<h3>操作步骤：</h3>" +
                "<ol>" +
                "<li>在自动打开的浏览器中完成禅道登录</li>" +
                "<li>登录成功后，浏览器会显示一个特殊页面</li>" +
                "<li>该页面会自动提取Cookie并传递给插件</li>" +
                "<li>插件检测到登录成功后会自动关闭此对话框</li>" +
                "</ol>" +
                "<p><b>注意：</b>请保持浏览器窗口打开，直到看到成功提示。</p>" +
                "</html>")
        instructionPanel.add(stepsLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        // 添加状态指示器
        val progressBar = JProgressBar()
        progressBar.isIndeterminate = true
        progressBar.string = "正在等待登录..."
        progressBar.isStringPainted = true
        instructionPanel.add(progressBar)

        panel.add(instructionPanel, BorderLayout.CENTER)

        // 开始监控登录状态
        startLoginMonitoring()

        return panel
    }
    
    private fun createLocalHtmlAndOpenBrowser() {
        try {
            // 创建临时HTML文件
            htmlFile = File.createTempFile("chandao_login", ".html")

            val htmlContent = createLoginHtml()
            htmlFile?.writeText(htmlContent, Charsets.UTF_8)

            // 在浏览器中打开HTML文件
            Desktop.getDesktop().browse(htmlFile?.toURI())

            println("Local HTML file created: ${htmlFile?.absolutePath}")

        } catch (e: Exception) {
            println("Failed to create local HTML file: ${e.message}")
            // 降级到直接打开禅道页面
            BrowserUtil.browse(initialUrl)
        }
    }
    
    private fun createLoginHtml(): String {
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>禅道登录 - IDEA插件</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .step {
            background: rgba(255, 255, 255, 0.2);
            margin: 15px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        .button {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4CAF50;
        }
        .iframe-container {
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 禅道登录助手</h1>
            <p>IDEA插件内嵌登录页面</p>
        </div>

        <div class="step">
            <h3>📋 操作说明</h3>
            <p>1. 点击下方按钮在新窗口中打开禅道登录页面</p>
            <p>2. 在新窗口中完成登录</p>
            <p>3. 登录成功后返回此页面点击"检测登录状态"</p>
            <p>4. 系统会自动提取Cookie并传递给IDEA插件</p>
        </div>

        <div style="text-align: center;">
            <button class="button" onclick="openChandaoWindow()">🔗 打开禅道登录页面</button>
            <button class="button" onclick="checkLoginAndExtract()" id="checkBtn">🔍 检测登录状态</button>
        </div>

        <div id="status" class="status">
            点击上方按钮开始登录流程
        </div>

        <div class="step" id="instructions" style="display:none;">
            <h3>🎯 登录检测</h3>
            <p>请确保您已在新窗口中完成禅道登录，然后点击"检测登录状态"按钮。</p>
            <p>系统会尝试访问禅道API来验证登录状态并提取必要的认证信息。</p>
        </div>
    </div>

    <script>
        let chandaoWindow = null;

        function openChandaoWindow() {
            const status = document.getElementById('status');
            const instructions = document.getElementById('instructions');

            // 在新窗口中打开禅道登录页面
            chandaoWindow = window.open('$initialUrl', 'chandaoLogin', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            if (chandaoWindow) {
                status.innerHTML = '🔄 已在新窗口中打开禅道登录页面，请完成登录后返回此页面';
                instructions.style.display = 'block';

                // 监控窗口关闭
                const checkClosed = setInterval(() => {
                    if (chandaoWindow.closed) {
                        clearInterval(checkClosed);
                        status.innerHTML = '🔄 登录窗口已关闭，请点击"检测登录状态"按钮';
                    }
                }, 1000);
            } else {
                status.innerHTML = '❌ 无法打开新窗口，请检查浏览器弹窗设置';
            }
        }

        function checkLoginAndExtract() {
            const status = document.getElementById('status');
            status.innerHTML = '🔍 正在检测登录状态...';

            // 尝试通过fetch请求检测登录状态
            fetch('$initialUrl', {
                method: 'GET',
                credentials: 'include',
                mode: 'no-cors'
            }).then(() => {
                // 由于no-cors模式，我们无法读取响应，但可以尝试提取cookie
                extractCookiesFromCurrentDomain();
            }).catch(e => {
                console.log('Fetch failed, trying alternative method:', e);
                extractCookiesFromCurrentDomain();
            });
        }

        function extractCookiesFromCurrentDomain() {
            try {
                // 获取当前域的所有cookie
                let cookies = {};
                document.cookie.split(';').forEach(cookie => {
                    const [name, value] = cookie.trim().split('=');
                    if (name && value) {
                        cookies[name] = value;
                    }
                });

                // 创建一个模拟的登录状态
                const loginData = {
                    timestamp: new Date().toISOString(),
                    cookies: cookies,
                    loginConfirmed: true,
                    source: 'window_login'
                };

                // 显示结果
                const status = document.getElementById('status');
                status.innerHTML = '✅ 登录状态检测完成！正在传递信息给IDEA插件...';
                status.className = 'status success';

                // 保存登录信息
                saveCookiesToFile(loginData);

            } catch (e) {
                document.getElementById('status').innerHTML = '❌ 检测登录状态时发生错误: ' + e.message;
                console.error('Login detection error:', e);
            }
        }

        function saveCookiesToFile(loginData) {
            // 尝试通过本地存储传递数据
            localStorage.setItem('chandao_login_data', JSON.stringify(loginData));

            // 尝试创建一个下载链接来保存数据到文件
            try {
                const dataStr = JSON.stringify(loginData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = 'chandao_login_data.json';
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                // 显示成功消息
                setTimeout(() => {
                    document.getElementById('status').innerHTML =
                        '🎉 登录信息已成功保存！<br>' +
                        '请返回IDEA插件点击"确认登录"按钮完成流程。<br>' +
                        '<small>已保存登录数据到下载文件夹</small>';
                }, 1000);

            } catch (e) {
                console.error('Error saving to file:', e);
                // 显示成功消息（即使文件保存失败，本地存储仍然可用）
                setTimeout(() => {
                    document.getElementById('status').innerHTML =
                        '🎉 登录信息已保存到本地存储！<br>' +
                        '请返回IDEA插件点击"确认登录"按钮完成流程。';
                }, 1000);
            }
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            console.log('Chandao login helper loaded');
        };
    </script>
</body>
</html>
        """.trimIndent()
    }
    
    private fun startLoginMonitoring() {
        refreshJob = CoroutineScope(Dispatchers.IO).launch {
            while (!isLoginDetected && isActive) {
                try {
                    // 检查本地存储文件是否有Cookie数据
                    checkForCookieFile()

                    delay(2000) // 每2秒检查一次
                } catch (e: Exception) {
                    println("Error in login monitoring: ${e.message}")
                }
            }
        }
    }
    
    private fun checkForCookieFile() {
        try {
            // 检查下载文件夹中的登录数据文件
            val userHome = System.getProperty("user.home")
            val downloadDir = File(userHome, "Downloads")
            val loginDataFile = File(downloadDir, "chandao_login_data.json")

            if (loginDataFile.exists()) {
                val loginData = loginDataFile.readText()
                println("Found login data file: $loginData")

                // 解析登录数据
                val cookies = parseLoginData(loginData)
                if (cookies.isNotEmpty()) {
                    SwingUtilities.invokeLater {
                        handleLoginSuccess(cookies)
                    }
                }

                // 删除文件
                loginDataFile.delete()
                return
            }

            // 备用：检查临时目录
            val tempDir = System.getProperty("java.io.tmpdir")
            val cookieFile = File(tempDir, "chandao_cookies.json")

            if (cookieFile.exists()) {
                val cookieData = cookieFile.readText()
                println("Found cookie file: $cookieData")

                // 解析Cookie数据
                val cookies = parseCookieData(cookieData)
                if (cookies.isNotEmpty()) {
                    SwingUtilities.invokeLater {
                        handleLoginSuccess(cookies)
                    }
                }

                // 删除临时文件
                cookieFile.delete()
            }

        } catch (e: Exception) {
            println("Error checking cookie file: ${e.message}")
        }
    }

    private fun parseLoginData(loginData: String): Map<String, String> {
        val cookies = mutableMapOf<String, String>()

        try {
            // 检查是否包含loginConfirmed标记
            if (loginData.contains("\"loginConfirmed\"") && loginData.contains("true")) {
                // 用户已确认登录，创建确认标记
                cookies["user_confirmed_login"] = "true"

                // 尝试提取任何可用的Cookie
                val phpSessionMatch = Regex("\"PHPSESSID\"\\s*:\\s*\"([^\"]+)\"").find(loginData)
                val zentaoSidMatch = Regex("\"zentaosid\"\\s*:\\s*\"([^\"]+)\"").find(loginData)

                phpSessionMatch?.groupValues?.get(1)?.let { cookies["PHPSESSID"] = it }
                zentaoSidMatch?.groupValues?.get(1)?.let { cookies["zentaosid"] = it }
            }
        } catch (e: Exception) {
            println("Error parsing login data: ${e.message}")
        }

        return cookies
    }

    private fun parseCookieData(cookieData: String): Map<String, String> {
        val cookies = mutableMapOf<String, String>()

        try {
            // 简单的JSON解析（实际项目中应该使用Gson）
            if (cookieData.contains("PHPSESSID") || cookieData.contains("zentaosid")) {
                // 提取关键Cookie
                val phpSessionMatch = Regex("\"PHPSESSID\"\\s*:\\s*\"([^\"]+)\"").find(cookieData)
                val zentaoSidMatch = Regex("\"zentaosid\"\\s*:\\s*\"([^\"]+)\"").find(cookieData)

                phpSessionMatch?.groupValues?.get(1)?.let { cookies["PHPSESSID"] = it }
                zentaoSidMatch?.groupValues?.get(1)?.let { cookies["zentaosid"] = it }
            }
        } catch (e: Exception) {
            println("Error parsing cookie data: ${e.message}")
        }

        return cookies
    }
    
    private fun handleLoginSuccess(cookies: Map<String, String>) {
        if (isLoginDetected) return
        isLoginDetected = true

        refreshJob?.cancel()

        println("Login success detected with ${cookies.size} cookies")
        onLoginSuccess(cookies)
        close(OK_EXIT_CODE)
    }
    
    override fun createActions(): Array<Action> {
        val manualConfirmAction = object : AbstractAction("确认登录") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                // 手动确认登录，使用标记Cookie
                handleLoginSuccess(mapOf("user_confirmed_login" to "true"))
            }
        }

        val reopenAction = object : AbstractAction("重新打开浏览器") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                createLocalHtmlAndOpenBrowser()
            }
        }

        return arrayOf(manualConfirmAction, reopenAction, cancelAction)
    }
    
    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }
    
    override fun getPreferredSize(): Dimension {
        return Dimension(1200, 800)
    }
    
    override fun dispose() {
        try {
            refreshJob?.cancel()
            htmlFile?.delete()
        } catch (e: Exception) {
            println("Error disposing resources: ${e.message}")
        }
        super.dispose()
    }
}
