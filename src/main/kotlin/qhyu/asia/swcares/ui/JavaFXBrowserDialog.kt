package qhyu.asia.swcares.ui

import com.intellij.ide.BrowserUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import kotlinx.coroutines.*
import okhttp3.*
import java.awt.BorderLayout
import java.awt.Desktop
import java.awt.Dimension
import java.io.File
import java.net.URI
import javax.swing.*

/**
 * 真正的内嵌浏览器 - 使用本地HTML文件和自动刷新
 */
class JavaFXBrowserDialog(
    project: Project?,
    private val initialUrl: String,
    private val onLoginSuccess: (cookies: Map<String, String>) -> Unit
) : DialogWrapper(project) {

    private var htmlFile: File? = null
    private var refreshJob: Job? = null
    private var isLoginDetected = false
    private val client = OkHttpClient.Builder()
        .cookieJar(CookieJar.NO_COOKIES)
        .build()
    
    init {
        title = "禅道登录 - 内嵌浏览器"
        init()
        setSize(1200, 800)
        setResizable(true)

        // 创建本地HTML文件并打开浏览器
        createLocalHtmlAndOpenBrowser()
    }
    
    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())

        // 创建说明面板
        val instructionPanel = JPanel()
        instructionPanel.layout = BoxLayout(instructionPanel, BoxLayout.Y_AXIS)
        instructionPanel.border = JBUI.Borders.empty(20)

        val titleLabel = JBLabel("<html><h2>🌐 IDEA内嵌网页登录</h2></html>")
        titleLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(titleLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        val statusLabel = JBLabel("<html><b>状态：</b>浏览器已在外部打开，正在监控登录状态...</html>")
        statusLabel.alignmentX = JComponent.CENTER_ALIGNMENT
        instructionPanel.add(statusLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        val stepsLabel = JBLabel("<html>" +
                "<h3>操作步骤：</h3>" +
                "<ol>" +
                "<li>在自动打开的浏览器中完成禅道登录</li>" +
                "<li>登录成功后，浏览器会显示一个特殊页面</li>" +
                "<li>该页面会自动提取Cookie并传递给插件</li>" +
                "<li>插件检测到登录成功后会自动关闭此对话框</li>" +
                "</ol>" +
                "<p><b>注意：</b>请保持浏览器窗口打开，直到看到成功提示。</p>" +
                "</html>")
        instructionPanel.add(stepsLabel)

        instructionPanel.add(Box.createVerticalStrut(20))

        // 添加状态指示器
        val progressBar = JProgressBar()
        progressBar.isIndeterminate = true
        progressBar.string = "正在等待登录..."
        progressBar.isStringPainted = true
        instructionPanel.add(progressBar)

        panel.add(instructionPanel, BorderLayout.CENTER)

        // 开始监控登录状态
        startLoginMonitoring()

        return panel
    }
    
    private fun createLocalHtmlAndOpenBrowser() {
        try {
            // 创建临时HTML文件
            htmlFile = File.createTempFile("chandao_login", ".html")

            val htmlContent = createLoginHtml()
            htmlFile?.writeText(htmlContent, Charsets.UTF_8)

            // 在浏览器中打开HTML文件
            Desktop.getDesktop().browse(htmlFile?.toURI())

            println("Local HTML file created: ${htmlFile?.absolutePath}")

        } catch (e: Exception) {
            println("Failed to create local HTML file: ${e.message}")
            // 降级到直接打开禅道页面
            BrowserUtil.browse(initialUrl)
        }
    }
    
    private fun createLoginHtml(): String {
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>禅道登录 - IDEA插件</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .step {
            background: rgba(255, 255, 255, 0.2);
            margin: 15px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        .button {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4CAF50;
        }
        .iframe-container {
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 禅道登录助手</h1>
            <p>IDEA插件内嵌登录页面</p>
        </div>

        <div class="step">
            <h3>📋 操作说明</h3>
            <p>1. 点击下方按钮在内嵌框架中打开禅道登录页面</p>
            <p>2. 在框架中完成登录</p>
            <p>3. 登录成功后点击"提取Cookie"按钮</p>
            <p>4. Cookie会自动传递给IDEA插件</p>
        </div>

        <div style="text-align: center;">
            <button class="button" onclick="loadChandao()">🔗 加载禅道登录页面</button>
            <button class="button" onclick="extractCookies()" id="extractBtn" style="display:none;">🍪 提取Cookie</button>
        </div>

        <div id="status" class="status">
            点击上方按钮开始登录流程
        </div>

        <div class="iframe-container" id="iframeContainer" style="display:none;">
            <iframe id="chandaoFrame" src="about:blank"></iframe>
        </div>
    </div>

    <script>
        function loadChandao() {
            const iframe = document.getElementById('chandaoFrame');
            const container = document.getElementById('iframeContainer');
            const status = document.getElementById('status');
            const extractBtn = document.getElementById('extractBtn');

            iframe.src = '$initialUrl';
            container.style.display = 'block';
            extractBtn.style.display = 'inline-block';
            status.innerHTML = '🔄 正在加载禅道登录页面...';

            iframe.onload = function() {
                status.innerHTML = '✅ 禅道页面已加载，请在上方框架中完成登录';

                // 定期检查登录状态
                setInterval(checkLoginStatus, 3000);
            };
        }

        function checkLoginStatus() {
            try {
                const iframe = document.getElementById('chandaoFrame');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const currentUrl = iframe.contentWindow.location.href;

                // 检查是否登录成功（URL包含特定路径）
                if (currentUrl.includes('/my-work-task.html') ||
                    currentUrl.includes('/my/') ||
                    currentUrl.includes('/index')) {

                    document.getElementById('status').innerHTML = '🎉 检测到登录成功！请点击"提取Cookie"按钮';
                    document.getElementById('extractBtn').style.background = '#FF9800';
                    document.getElementById('extractBtn').innerHTML = '🎯 立即提取Cookie';
                }
            } catch (e) {
                // 跨域限制，无法访问iframe内容
                console.log('Cannot access iframe content due to CORS policy');
            }
        }

        function extractCookies() {
            try {
                const iframe = document.getElementById('chandaoFrame');
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                // 尝试从iframe获取cookie
                let cookies = {};
                if (iframeDoc.cookie) {
                    iframeDoc.cookie.split(';').forEach(cookie => {
                        const [name, value] = cookie.trim().split('=');
                        if (name && value) {
                            cookies[name] = value;
                        }
                    });
                }

                // 如果无法获取iframe的cookie，使用当前页面的cookie
                if (Object.keys(cookies).length === 0) {
                    document.cookie.split(';').forEach(cookie => {
                        const [name, value] = cookie.trim().split('=');
                        if (name && value) {
                            cookies[name] = value;
                        }
                    });
                }

                // 显示提取结果
                const status = document.getElementById('status');
                if (Object.keys(cookies).length > 0) {
                    status.innerHTML = '✅ 成功提取到 ' + Object.keys(cookies).length + ' 个Cookie！正在传递给IDEA插件...';
                    status.className = 'status success';

                    // 将cookie信息写入到一个特殊的文件中，供插件读取
                    saveCookiesToFile(cookies);
                } else {
                    status.innerHTML = '⚠️ 未能提取到Cookie，请确保已在上方框架中完成登录';
                }

            } catch (e) {
                document.getElementById('status').innerHTML = '❌ 提取Cookie时发生错误: ' + e.message;
                console.error('Cookie extraction error:', e);
            }
        }

        function saveCookiesToFile(cookies) {
            // 创建cookie数据
            const cookieData = {
                timestamp: new Date().toISOString(),
                cookies: cookies,
                source: 'embedded_browser'
            };

            // 尝试通过本地存储传递数据
            localStorage.setItem('chandao_cookies', JSON.stringify(cookieData));

            // 显示成功消息
            setTimeout(() => {
                document.getElementById('status').innerHTML =
                    '🎉 Cookie已成功提取并保存！<br>' +
                    '请返回IDEA插件点击"确认登录"按钮完成流程。<br>' +
                    '<small>提取的Cookie: ' + Object.keys(cookies).join(', ') + '</small>';
            }, 1000);
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            console.log('Chandao login helper loaded');
        };
    </script>
</body>
</html>
        """.trimIndent()
    }
    
    private fun startLoginMonitoring() {
        refreshJob = CoroutineScope(Dispatchers.IO).launch {
            while (!isLoginDetected && isActive) {
                try {
                    // 检查本地存储文件是否有Cookie数据
                    checkForCookieFile()

                    delay(2000) // 每2秒检查一次
                } catch (e: Exception) {
                    println("Error in login monitoring: ${e.message}")
                }
            }
        }
    }
    
    private fun checkForCookieFile() {
        try {
            // 检查是否有浏览器创建的Cookie文件
            val tempDir = System.getProperty("java.io.tmpdir")
            val cookieFile = File(tempDir, "chandao_cookies.json")

            if (cookieFile.exists()) {
                val cookieData = cookieFile.readText()
                println("Found cookie file: $cookieData")

                // 解析Cookie数据
                val cookies = parseCookieData(cookieData)
                if (cookies.isNotEmpty()) {
                    SwingUtilities.invokeLater {
                        handleLoginSuccess(cookies)
                    }
                }

                // 删除临时文件
                cookieFile.delete()
            }

        } catch (e: Exception) {
            println("Error checking cookie file: ${e.message}")
        }
    }

    private fun parseCookieData(cookieData: String): Map<String, String> {
        val cookies = mutableMapOf<String, String>()

        try {
            // 简单的JSON解析（实际项目中应该使用Gson）
            if (cookieData.contains("PHPSESSID") || cookieData.contains("zentaosid")) {
                // 提取关键Cookie
                val phpSessionMatch = Regex("\"PHPSESSID\"\\s*:\\s*\"([^\"]+)\"").find(cookieData)
                val zentaoSidMatch = Regex("\"zentaosid\"\\s*:\\s*\"([^\"]+)\"").find(cookieData)

                phpSessionMatch?.groupValues?.get(1)?.let { cookies["PHPSESSID"] = it }
                zentaoSidMatch?.groupValues?.get(1)?.let { cookies["zentaosid"] = it }
            }
        } catch (e: Exception) {
            println("Error parsing cookie data: ${e.message}")
        }

        return cookies
    }
    
    private fun handleLoginSuccess(cookies: Map<String, String>) {
        if (isLoginDetected) return
        isLoginDetected = true

        refreshJob?.cancel()

        println("Login success detected with ${cookies.size} cookies")
        onLoginSuccess(cookies)
        close(OK_EXIT_CODE)
    }
    
    override fun createActions(): Array<Action> {
        val manualConfirmAction = object : AbstractAction("确认登录") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                // 手动确认登录，使用标记Cookie
                handleLoginSuccess(mapOf("user_confirmed_login" to "true"))
            }
        }

        val reopenAction = object : AbstractAction("重新打开浏览器") {
            override fun actionPerformed(e: java.awt.event.ActionEvent?) {
                createLocalHtmlAndOpenBrowser()
            }
        }

        return arrayOf(manualConfirmAction, reopenAction, cancelAction)
    }
    
    override fun getCancelAction(): Action {
        val action = super.getCancelAction()
        action.putValue(Action.NAME, "取消")
        return action
    }
    
    override fun getPreferredSize(): Dimension {
        return Dimension(1200, 800)
    }
    
    override fun dispose() {
        try {
            refreshJob?.cancel()
            htmlFile?.delete()
        } catch (e: Exception) {
            println("Error disposing resources: ${e.message}")
        }
        super.dispose()
    }
}
