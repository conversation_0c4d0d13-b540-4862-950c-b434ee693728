# 🌐 内嵌浏览器登录指南

## 功能说明

现在插件提供了真正的**IDEA内嵌浏览器**功能，您可以直接在IDEA内部完成禅道登录，无需切换到外部浏览器。

### ✨ **核心特性**

1. **真正的内嵌浏览器**
   - 在IDEA对话框内直接显示禅道网页
   - 1200x800像素大窗口，提供良好的浏览体验
   - 支持所有网页交互功能

2. **无缝登录体验**
   - 在内嵌浏览器中直接输入用户名密码
   - 登录成功后点击"确认登录"按钮
   - 自动提取登录状态并开始数据爬取

3. **智能Cookie管理**
   - 自动从内嵌浏览器提取Cookie
   - 持久化保存，下次自动使用
   - 支持调试和状态检查

## 🚀 **使用步骤**

### 1. 启动插件
```bash
export JAVA_HOME=$(/usr/libexec/java_home -v17)
./gradlew runIde
```

### 2. 选择内嵌浏览器登录
1. 在IDEA中点击 `Tools` → `打开禅道`
2. 在弹出的对话框中选择 **"在IDEA内嵌浏览器中登录"**

### 3. 在内嵌浏览器中登录
1. 会弹出一个大的对话框，内部显示禅道登录页面
2. 直接在这个内嵌浏览器中：
   - 输入您的用户名和密码
   - 点击登录按钮
   - 等待登录成功

### 4. 确认登录并开始爬取
1. 登录成功后，点击对话框底部的 **"手动确认登录"** 按钮
2. 插件会自动：
   - 提取浏览器中的Cookie
   - 保存登录状态
   - 开始爬取任务数据
   - 在工具窗口中显示结果

## 🔧 **技术优势**

### 相比外部浏览器登录：
- ✅ **无需切换窗口**：所有操作在IDEA内完成
- ✅ **自动Cookie提取**：无需手动复制粘贴
- ✅ **更好的集成**：与IDE无缝融合
- ✅ **调试友好**：可以查看浏览器控制台

### 相比对话框登录：
- ✅ **支持复杂认证**：验证码、多因子认证等
- ✅ **完整网页体验**：支持所有网页功能
- ✅ **兼容性更好**：适用于各种登录方式

## 🛠 **故障排查**

### 1. 内嵌浏览器不显示
**可能原因**：
- IDE版本不支持JCEF
- 系统缺少必要组件

**解决方法**：
- 确保使用IntelliJ IDEA 2020.1或更新版本
- 选择"在外部浏览器中登录"作为备选方案

### 2. 登录后无法爬取数据
**检查步骤**：
1. 确认在内嵌浏览器中成功登录
2. 尝试在内嵌浏览器中访问 `https://chandao.sw/pro/my-work-task.html`
3. 点击"手动确认登录"按钮
4. 查看IDE日志中的Cookie提取信息

### 3. Cookie提取失败
**调试方法**：
1. 在内嵌浏览器中按F12打开开发者工具
2. 在Console中运行：
   ```javascript
   console.log(document.cookie);
   ```
3. 检查是否有PHPSESSID等关键Cookie

### 4. 页面加载失败
**可能原因**：
- 网络连接问题
- SSL证书问题
- 防火墙阻止

**解决方法**：
- 检查网络连接
- 确认可以访问 `https://chandao.sw/pro`
- 尝试在外部浏览器中先访问一次

## 📋 **使用技巧**

### 1. 最佳实践
- 首次使用时选择内嵌浏览器登录
- 登录成功后，后续使用会自动加载保存的Cookie
- 定期清理过期的Cookie（插件会自动处理）

### 2. 调试技巧
- 在内嵌浏览器中可以使用F12开发者工具
- 查看Console输出了解Cookie提取情况
- 检查Network标签确认请求是否成功

### 3. 性能优化
- 内嵌浏览器会占用一定内存，使用完毕后会自动释放
- 如果遇到性能问题，可以选择外部浏览器登录

## 🔄 **工作流程**

```
启动插件 → 选择内嵌浏览器登录 → 在内嵌浏览器中登录 → 
确认登录 → 自动提取Cookie → 爬取数据 → 显示结果
```

## 🆚 **登录方式对比**

| 登录方式 | 优点 | 缺点 | 推荐场景 |
|---------|------|------|----------|
| **内嵌浏览器** | 无缝集成、自动提取Cookie | 需要JCEF支持 | **首选方案** |
| 外部浏览器 | 兼容性好、功能完整 | 需要手动操作 | 内嵌浏览器不可用时 |
| 对话框登录 | 简单快速 | 不支持复杂认证 | 简单密码认证 |

## 🎯 **预期效果**

使用内嵌浏览器登录后：

1. **首次使用**：
   - 在内嵌浏览器中完成登录
   - 点击确认后自动获取数据
   - Cookie自动保存到本地

2. **后续使用**：
   - 启动插件后自动检测保存的Cookie
   - 如果Cookie有效，直接显示"自动登录成功"
   - 无需重复登录，直接获取最新数据

---

**总结**：内嵌浏览器提供了最佳的用户体验，将外部网页完全集成到IDEA中，实现了真正的"一站式"操作体验。
