# SW Cares - 禅道数据爬取插件

这是一个为 IntelliJ IDEA 开发的插件，用于在 IDE 中打开禅道网站，处理用户登录，并爬取相关记录。

## 功能特性

- 🌐 **多种登录方式**：
  - **嵌入式浏览器登录**：在对话框中自动打开浏览器，用户手动登录后确认
  - **外部浏览器登录**：在外部浏览器中完成登录
  - **对话框登录**：在 IDE 内直接输入用户名密码
- 📊 **数据展示**：在工具窗口中以表格形式展示爬取的数据
- 🔄 **实时刷新**：支持手动刷新数据
- 🔗 **快速访问**：双击记录可在浏览器中打开对应链接
- 🍪 **会话管理**：自动管理登录会话和 Cookie
- 🔒 **安全性**：支持内网 SSL 证书，不保存敏感信息

## 安装和使用

### 构建插件

1. 确保您的系统安装了 Java 17 或更高版本
2. 在项目根目录执行：
   ```bash
   export JAVA_HOME=$(/usr/libexec/java_home -v17)
   ./gradlew build
   ```

### 运行插件

在开发环境中测试插件：
```bash
export JAVA_HOME=$(/usr/libexec/java_home -v17)
./gradlew runIde
```

### 使用插件

1. **启动插件**：
   - 在 IntelliJ IDEA 中，点击 `Tools` 菜单
   - 选择 `打开禅道` 选项

2. **登录方式**：
   - **嵌入式浏览器登录**（推荐）：
     - 插件会自动在外部浏览器中打开禅道登录页面
     - 在浏览器中完成登录
     - 返回对话框，点击"确认登录"按钮
     - 高级用户可以手动输入会话信息
   - **外部浏览器登录**：插件在浏览器中打开禅道登录页面，登录后点击"开始爬取"
   - **对话框登录**：在弹出的对话框中直接输入用户名和密码

3. **查看数据**：
   - 登录成功后，数据会显示在右侧的 "SW Cares" 工具窗口中
   - 双击任意记录可在浏览器中打开对应的禅道页面
   - 右键点击记录可复制链接

4. **工具窗口功能**：
   - **刷新数据**：点击刷新按钮获取最新数据
   - **在浏览器中打开**：直接打开禅道我的页面
   - **登出**：清除登录状态和本地数据

## 项目结构

```
src/main/kotlin/qhyu/asia/swcares/
├── actions/
│   └── OpenChandaoAction.kt          # 主要的 Action 类
├── model/
│   └── ChandaoDataModel.kt           # 数据模型
├── service/
│   └── ChandaoWebService.kt          # 网络服务和数据爬取
└── ui/
    ├── ChandaoToolWindowFactory.kt   # 工具窗口工厂
    ├── ChandaoToolWindowPanel.kt     # 工具窗口面板
    └── LoginDialog.kt                # 登录对话框
```

## 技术栈

- **Kotlin**：主要开发语言
- **IntelliJ Platform SDK**：插件开发框架
- **OkHttp**：HTTP 客户端，用于网络请求
- **Jsoup**：HTML 解析库，用于数据提取
- **Gson**：JSON 序列化/反序列化
- **Kotlin Coroutines**：异步编程

## 配置说明

默认配置在 `CrawlerConfig` 中：
- **baseUrl**: `https://chandao.sw/pro`
- **loginPath**: `/user-login.html`
- **myPath**: `/my/`
- **myWorkTaskPath**: `/my-work-task.html` （主要爬取页面）
- **timeout**: 30秒
- **retryCount**: 3次

## 数据结构

插件会爬取禅道"我的工作-任务"页面的以下字段：
- **ID**: 任务ID
- **优先级**: 任务优先级 (P)
- **任务名称**: 任务的具体名称
- **所属项目**: 任务所属的项目
- **所属版本**: 项目版本
- **创建者**: 任务创建人
- **指派给**: 任务指派对象
- **预计**: 预计工时
- **消耗**: 已消耗工时
- **剩余**: 剩余工时
- **进度**: 任务完成进度
- **认工日期**: 认工日期
- **截止日期**: 任务截止日期
- **状态**: 任务当前状态

## 注意事项

1. **网络访问**：确保您的网络可以访问禅道服务器
2. **登录凭据**：插件不会保存您的登录密码
3. **数据解析**：数据解析逻辑基于禅道的标准页面结构，如果页面结构发生变化，可能需要调整解析代码
4. **会话管理**：登录会话会在插件运行期间保持，重启 IDE 后需要重新登录

## 开发说明

### 自定义数据解析

如果需要修改数据解析逻辑，请编辑 `ChandaoWebService.kt` 中的 `parseMyPageData` 方法：

```kotlin
private fun parseMyPageData(html: String): List<ChandaoRecord> {
    // 根据实际的禅道页面结构调整解析逻辑
    // ...
}
```

### 添加新功能

1. 在相应的包中添加新的类文件
2. 在 `plugin.xml` 中注册新的扩展点或 Action
3. 更新 UI 组件以支持新功能

## 故障排除

### 常见问题

1. **构建失败**：确保使用 Java 17 或更高版本
2. **登录失败**：检查网络连接和登录凭据
3. **数据为空**：可能是页面结构变化，需要调整解析逻辑
4. **工具窗口不显示**：检查插件是否正确安装和启用

### 日志查看

插件使用 IntelliJ 的日志系统，可以在 IDE 的日志中查看详细信息：
- macOS: `~/Library/Logs/JetBrains/IntelliJIdea{version}/idea.log`
- Windows: `%APPDATA%\JetBrains\IntelliJIdea{version}\log\idea.log`
- Linux: `~/.cache/JetBrains/IntelliJIdea{version}/log/idea.log`

## 许可证

本项目仅供学习和内部使用。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个插件。
