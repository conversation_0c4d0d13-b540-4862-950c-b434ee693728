# 禅道插件演示指南

## 快速演示步骤

### 1. 启动插件
```bash
# 确保使用 Java 17
export JAVA_HOME=$(/usr/libexec/java_home -v17)

# 启动开发环境
./gradlew runIde
```

### 2. 使用插件

#### 步骤 1: 打开插件
- 在 IntelliJ IDEA 中，点击顶部菜单 `Tools`
- 选择 `打开禅道`

#### 步骤 2: 选择登录方式
会弹出一个对话框，提供4种登录选项：

1. **在内嵌浏览器中登录** ⭐ **推荐**
2. 在外部浏览器中登录
3. 在对话框中登录
4. 取消

#### 步骤 3: 完成登录（以推荐方式为例）

**选择 "在内嵌浏览器中登录"**：

1. 会弹出一个登录助手对话框
2. 同时自动在您的默认浏览器中打开禅道登录页面
3. 在浏览器中输入您的用户名和密码完成登录
4. 登录成功后，返回到登录助手对话框
5. 点击 "确认登录" 按钮

#### 步骤 4: 查看数据
- 登录成功后，右侧会出现 "SW Cares" 工具窗口
- 工具窗口中显示一个表格，包含从禅道爬取的数据
- 表格包含以下列：
  - ID
  - 标题
  - 类型
  - 状态
  - 优先级
  - 指派给

#### 步骤 5: 交互功能
- **双击记录**：在浏览器中打开对应的禅道页面
- **右键菜单**：
  - "在浏览器中打开"
  - "复制链接"
- **工具栏按钮**：
  - "刷新数据"：获取最新数据
  - "在浏览器中打开"：打开禅道主页
  - "登出"：清除登录状态

## 演示要点

### 🎯 核心亮点

1. **用户友好的登录流程**
   - 自动打开浏览器
   - 清晰的步骤指引
   - 多种登录方式选择

2. **无缝集成体验**
   - 在 IDE 内完成所有操作
   - 工具窗口直观显示数据
   - 快速访问禅道页面

3. **安全性考虑**
   - 不保存用户密码
   - 支持内网 SSL 证书
   - 会话管理安全

### 🔧 技术特性

1. **多平台支持**
   - 基于 IntelliJ Platform
   - 支持 macOS、Windows、Linux

2. **现代技术栈**
   - Kotlin 开发
   - 协程异步处理
   - OkHttp 网络请求
   - Jsoup HTML 解析

3. **可扩展架构**
   - 模块化设计
   - 易于添加新功能
   - 完善的错误处理

## 常见演示场景

### 场景 1: 首次使用
- 演示完整的登录流程
- 展示数据爬取和显示
- 介绍各种交互功能

### 场景 2: 日常使用
- 快速刷新数据
- 直接访问特定记录
- 工具窗口的便利性

### 场景 3: 高级功能
- 手动输入会话信息
- 错误处理和恢复
- 多种登录方式的选择

## 演示注意事项

### 环境准备
- ✅ 确保网络可以访问禅道服务器
- ✅ Java 17 或更高版本
- ✅ IntelliJ IDEA 2025.1 或兼容版本

### 可能遇到的问题
- **网络连接问题**：确保可以访问 `https://chandao.sw/pro`
- **SSL 证书问题**：插件已配置忽略内网证书验证
- **浏览器未打开**：可以点击"重新打开浏览器"按钮

### 演示技巧
1. **准备测试数据**：确保禅道中有一些测试记录
2. **网络稳定**：确保演示环境网络稳定
3. **备用方案**：如果嵌入式登录有问题，可以使用外部浏览器登录

## 扩展演示

### 开发者视角
- 展示代码结构
- 说明扩展点
- 演示如何添加新功能

### 用户体验视角
- 对比传统方式的优势
- 展示工作流程的改进
- 强调便利性和效率提升

## 反馈收集

演示后可以收集以下反馈：
- 登录流程是否直观
- 数据显示是否满足需求
- 还需要哪些功能
- 性能和稳定性如何
- 界面设计是否友好

---

**提示**: 这个插件展示了如何将外部 Web 应用无缝集成到 IDE 中，提供了一个完整的解决方案，从用户认证到数据展示，再到交互功能。
